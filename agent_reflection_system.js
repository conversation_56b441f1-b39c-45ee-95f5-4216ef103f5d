#!/usr/bin/env node

/**
 * 🧠 SYSTÈME DE QUESTIONNEMENT RÉFLEXIF POUR AGENT
 * 
 * Force l'agent à réfléchir sur ses réponses avec des questions ciblées
 * Stimule la mémoire thermique et améliore la qualité des réponses
 * Contrôle par bouton pause/reprise
 * 
 * Jean-Luc PASSAVE - 2025
 */

const fs = require('fs');

class AgentReflectionSystem {
    constructor() {
        this.thermalMemoryPath = './thermal_memory_real_clones_1749979850296.json';
        this.reflectionEnabled = true;
        this.reflectionHistory = [];
        this.questionPatterns = [];
        this.lastResponse = null;
        this.reflectionCounter = 0;
        
        console.log('🧠 SYSTÈME DE QUESTIONNEMENT RÉFLEXIF');
        console.log('🎯 Objectif: Stimuler la réflexion de l\'agent');
        console.log('💭 Améliorer la qualité des réponses');
        
        this.setupReflectionQuestions();
    }
    
    // Configuration des questions de réflexion
    setupReflectionQuestions() {
        this.questionPatterns = [
            {
                category: 'SATISFACTION',
                questions: [
                    'Es-tu satisfait de ta dernière réponse ?',
                    'Penses-tu avoir donné la meilleure réponse possible ?',
                    'Ta réponse répond-elle complètement à la question ?',
                    'Y a-t-il des aspects que tu aurais pu mieux expliquer ?'
                ],
                frequency: 0.3, // 30% de chance
                priority: 'MEDIUM'
            },
            {
                category: 'COMPLETUDE',
                questions: [
                    'Ta réponse est-elle complète ?',
                    'As-tu oublié des éléments importants ?',
                    'Pourrais-tu ajouter des détails utiles ?',
                    'Y a-t-il des nuances à préciser ?'
                ],
                frequency: 0.25,
                priority: 'HIGH'
            },
            {
                category: 'PERSPECTIVE',
                questions: [
                    'Pourrais-tu envisager ta réponse sous un autre angle ?',
                    'Y a-t-il une approche différente à considérer ?',
                    'Comment Jean-Luc pourrait-il interpréter ta réponse ?',
                    'Existe-t-il des alternatives à ta solution ?'
                ],
                frequency: 0.2,
                priority: 'MEDIUM'
            },
            {
                category: 'MEMOIRE_THERMIQUE',
                questions: [
                    'As-tu utilisé toutes les informations de ta mémoire thermique ?',
                    'Y a-t-il des connexions neuronales pertinentes non exploitées ?',
                    'Peux-tu enrichir ta réponse avec tes souvenirs stockés ?',
                    'Ta mémoire thermique contient-elle des éléments complémentaires ?'
                ],
                frequency: 0.35,
                priority: 'HIGH'
            },
            {
                category: 'PRECISION',
                questions: [
                    'Ta réponse est-elle assez précise ?',
                    'Peux-tu être plus spécifique ?',
                    'Y a-t-il des termes techniques à clarifier ?',
                    'Tes exemples sont-ils suffisamment clairs ?'
                ],
                frequency: 0.2,
                priority: 'MEDIUM'
            },
            {
                category: 'INNOVATION',
                questions: [
                    'Y a-t-il une approche plus créative ?',
                    'Peux-tu proposer une solution innovante ?',
                    'Comment optimiser davantage ta réponse ?',
                    'Quelle serait la réponse idéale selon toi ?'
                ],
                frequency: 0.15,
                priority: 'LOW'
            }
        ];
        
        console.log(`✅ ${this.questionPatterns.length} catégories de questions configurées`);
    }
    
    // Sélectionner une question de réflexion
    selectReflectionQuestion(responseContext = {}) {
        if (!this.reflectionEnabled) return null;
        
        // Filtrer les catégories selon le contexte
        const availableCategories = this.questionPatterns.filter(category => {
            const random = Math.random();
            return random < category.frequency;
        });
        
        if (availableCategories.length === 0) return null;
        
        // Prioriser selon l'importance
        const priorityWeights = { HIGH: 3, MEDIUM: 2, LOW: 1 };
        const weightedCategories = [];
        
        availableCategories.forEach(category => {
            const weight = priorityWeights[category.priority] || 1;
            for (let i = 0; i < weight; i++) {
                weightedCategories.push(category);
            }
        });
        
        // Sélectionner une catégorie
        const selectedCategory = weightedCategories[Math.floor(Math.random() * weightedCategories.length)];
        
        // Sélectionner une question dans la catégorie
        const questions = selectedCategory.questions;
        const selectedQuestion = questions[Math.floor(Math.random() * questions.length)];
        
        return {
            category: selectedCategory.category,
            question: selectedQuestion,
            priority: selectedCategory.priority,
            timestamp: Date.now()
        };
    }
    
    // Traiter la réflexion de l'agent
    async processAgentReflection(originalResponse, reflectionPrompt) {
        try {
            this.reflectionCounter++;
            
            const reflection = {
                id: `reflection_${Date.now()}_${this.reflectionCounter}`,
                original_response: originalResponse,
                reflection_prompt: reflectionPrompt.question,
                category: reflectionPrompt.category,
                priority: reflectionPrompt.priority,
                timestamp: Date.now(),
                agent_id: 'deepseek_r1_8b',
                thermal_context_used: true
            };
            
            // Ajouter à l'historique
            this.reflectionHistory.push(reflection);
            
            // Sauvegarder dans la mémoire thermique
            await this.saveReflectionToThermalMemory(reflection);
            
            console.log(`🧠 Réflexion ${this.reflectionCounter}: ${reflectionPrompt.category}`);
            console.log(`💭 Question: ${reflectionPrompt.question}`);
            
            return reflection;
            
        } catch (error) {
            console.error('❌ Erreur traitement réflexion:', error.message);
            return null;
        }
    }
    
    // Sauvegarder la réflexion dans la mémoire thermique
    async saveReflectionToThermalMemory(reflection) {
        try {
            if (!fs.existsSync(this.thermalMemoryPath)) return false;
            
            const memory = JSON.parse(fs.readFileSync(this.thermalMemoryPath, 'utf8'));
            
            // Créer la zone de réflexion si elle n'existe pas
            if (!memory.thermal_zones) memory.thermal_zones = {};
            if (!memory.thermal_zones.zone_reflection) {
                memory.thermal_zones.zone_reflection = {
                    name: "Zone Réflexion Agent",
                    description: "Stockage des réflexions et questionnements de l'agent",
                    temperature: 37,
                    entries: []
                };
            }
            
            // Ajouter l'entrée de réflexion
            const reflectionEntry = {
                id: reflection.id,
                content: `RÉFLEXION AGENT - Catégorie: ${reflection.category}. Question: ${reflection.reflection_prompt}. Réponse originale: ${reflection.original_response.substring(0, 200)}... Stimulation mémoire thermique pour amélioration continue.`,
                importance: reflection.priority === 'HIGH' ? 0.9 : reflection.priority === 'MEDIUM' ? 0.7 : 0.5,
                timestamp: Math.floor(reflection.timestamp / 1000),
                synaptic_strength: 0.8,
                temperature: 37,
                zone: "zone_reflection",
                source: "agent_reflection_system",
                type: "agent_reflection",
                category: reflection.category,
                reflection_count: this.reflectionCounter,
                authentic: true
            };
            
            memory.thermal_zones.zone_reflection.entries.push(reflectionEntry);
            
            // Mettre à jour les statistiques
            if (!memory.reflection_system) {
                memory.reflection_system = {
                    enabled: true,
                    total_reflections: 0,
                    categories_used: [],
                    last_reflection: null,
                    improvement_score: 0
                };
            }
            
            memory.reflection_system.total_reflections = this.reflectionCounter;
            memory.reflection_system.last_reflection = reflection.timestamp;
            
            if (!memory.reflection_system.categories_used.includes(reflection.category)) {
                memory.reflection_system.categories_used.push(reflection.category);
            }
            
            // Sauvegarder
            fs.writeFileSync(this.thermalMemoryPath, JSON.stringify(memory, null, 2));
            
            return true;
            
        } catch (error) {
            console.error('❌ Erreur sauvegarde réflexion:', error.message);
            return false;
        }
    }
    
    // Activer/désactiver le système de réflexion
    toggleReflection(enabled = null) {
        if (enabled !== null) {
            this.reflectionEnabled = enabled;
        } else {
            this.reflectionEnabled = !this.reflectionEnabled;
        }
        
        const status = this.reflectionEnabled ? 'ACTIVÉ' : 'DÉSACTIVÉ';
        console.log(`🔄 Système de réflexion: ${status}`);
        
        return this.reflectionEnabled;
    }
    
    // Obtenir les statistiques de réflexion
    getReflectionStats() {
        const categories = {};
        this.reflectionHistory.forEach(reflection => {
            if (!categories[reflection.category]) {
                categories[reflection.category] = 0;
            }
            categories[reflection.category]++;
        });
        
        return {
            total_reflections: this.reflectionCounter,
            enabled: this.reflectionEnabled,
            categories_distribution: categories,
            recent_reflections: this.reflectionHistory.slice(-5),
            average_per_category: Object.keys(categories).length > 0 ? 
                this.reflectionCounter / Object.keys(categories).length : 0
        };
    }
    
    // Générer le code d'intégration pour l'interface
    generateInterfaceIntegration() {
        return `
// 🧠 SYSTÈME DE QUESTIONNEMENT RÉFLEXIF INTÉGRÉ
class ReflectionInterface {
    constructor() {
        this.reflectionEnabled = true;
        this.setupReflectionUI();
    }
    
    setupReflectionUI() {
        // Ajouter le bouton de contrôle
        const controlPanel = document.querySelector('.sidebar') || document.body;
        
        const reflectionControl = document.createElement('div');
        reflectionControl.className = 'reflection-control';
        reflectionControl.innerHTML = \`
            <div class="reflection-panel">
                <h4>🧠 Questionnement Réflexif</h4>
                <button id="toggle-reflection" class="reflection-btn active">
                    <span class="status">ACTIF</span>
                    <span class="icon">🔄</span>
                </button>
                <div class="reflection-stats">
                    <div>Réflexions: <span id="reflection-count">0</span></div>
                    <div>Statut: <span id="reflection-status">Activé</span></div>
                </div>
            </div>
        \`;
        
        controlPanel.appendChild(reflectionControl);
        
        // Ajouter les styles
        const style = document.createElement('style');
        style.textContent = \`
            .reflection-panel {
                background: linear-gradient(135deg, #1a1a2e, #16213e);
                border: 1px solid #ff69b4;
                border-radius: 10px;
                padding: 15px;
                margin: 10px 0;
                color: white;
            }
            
            .reflection-btn {
                background: linear-gradient(45deg, #ff69b4, #00d4ff);
                border: none;
                border-radius: 8px;
                padding: 10px 15px;
                color: white;
                cursor: pointer;
                width: 100%;
                display: flex;
                justify-content: space-between;
                align-items: center;
                font-weight: bold;
                transition: all 0.3s ease;
            }
            
            .reflection-btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 5px 15px rgba(255, 105, 180, 0.3);
            }
            
            .reflection-btn.inactive {
                background: linear-gradient(45deg, #666, #999);
            }
            
            .reflection-stats {
                margin-top: 10px;
                font-size: 12px;
                opacity: 0.8;
            }
            
            .reflection-question {
                background: rgba(255, 105, 180, 0.1);
                border-left: 3px solid #ff69b4;
                padding: 10px;
                margin: 10px 0;
                border-radius: 5px;
                font-style: italic;
            }
        \`;
        document.head.appendChild(style);
        
        // Ajouter les événements
        this.setupReflectionEvents();
    }
    
    setupReflectionEvents() {
        const toggleBtn = document.getElementById('toggle-reflection');
        const statusSpan = document.getElementById('reflection-status');
        
        toggleBtn.addEventListener('click', () => {
            this.reflectionEnabled = !this.reflectionEnabled;
            
            if (this.reflectionEnabled) {
                toggleBtn.classList.add('active');
                toggleBtn.classList.remove('inactive');
                toggleBtn.querySelector('.status').textContent = 'ACTIF';
                statusSpan.textContent = 'Activé';
            } else {
                toggleBtn.classList.remove('active');
                toggleBtn.classList.add('inactive');
                toggleBtn.querySelector('.status').textContent = 'PAUSE';
                statusSpan.textContent = 'En pause';
            }
            
            // Notifier le serveur
            fetch('/api/reflection/toggle', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ enabled: this.reflectionEnabled })
            });
        });
    }
    
    showReflectionQuestion(question, category) {
        if (!this.reflectionEnabled) return;
        
        const chatContainer = document.getElementById('chat-container');
        if (!chatContainer) return;
        
        const questionDiv = document.createElement('div');
        questionDiv.className = 'reflection-question';
        questionDiv.innerHTML = \`
            <strong>🧠 Réflexion (\${category}):</strong><br>
            \${question}
        \`;
        
        chatContainer.appendChild(questionDiv);
        chatContainer.scrollTop = chatContainer.scrollHeight;
        
        // Mettre à jour le compteur
        const countSpan = document.getElementById('reflection-count');
        if (countSpan) {
            const current = parseInt(countSpan.textContent) || 0;
            countSpan.textContent = current + 1;
        }
    }
}

// Initialiser le système de réflexion
const reflectionInterface = new ReflectionInterface();
`;
    }
    
    // Générer le rapport du système
    generateSystemReport() {
        const stats = this.getReflectionStats();
        
        const report = {
            timestamp: new Date().toISOString(),
            system_status: this.reflectionEnabled ? 'ACTIVE' : 'PAUSED',
            total_reflections: this.reflectionCounter,
            categories_configured: this.questionPatterns.length,
            reflection_distribution: stats.categories_distribution,
            recent_activity: stats.recent_reflections,
            memory_integration: 'ACTIVE',
            thermal_memory_path: this.thermalMemoryPath,
            created_by: 'Jean-Luc PASSAVE'
        };
        
        const reportPath = `./reflection_system_report_${Date.now()}.json`;
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        
        console.log('\n📊 RAPPORT SYSTÈME DE RÉFLEXION:');
        console.log(`📁 Rapport: ${reportPath}`);
        console.log(`🧠 Réflexions totales: ${report.total_reflections}`);
        console.log(`📋 Statut: ${report.system_status}`);
        console.log(`🎯 Catégories: ${report.categories_configured}`);
        
        return reportPath;
    }
}

// Exporter pour utilisation
module.exports = AgentReflectionSystem;

// Test si exécuté directement
if (require.main === module) {
    const reflectionSystem = new AgentReflectionSystem();
    
    console.log('\n🧪 TEST DU SYSTÈME DE RÉFLEXION:');
    
    // Test de sélection de question
    const question = reflectionSystem.selectReflectionQuestion();
    if (question) {
        console.log(`💭 Question sélectionnée: ${question.question}`);
        console.log(`📂 Catégorie: ${question.category}`);
        console.log(`⭐ Priorité: ${question.priority}`);
    }
    
    // Générer le code d'intégration
    const integrationCode = reflectionSystem.generateInterfaceIntegration();
    fs.writeFileSync('./reflection_interface_integration.js', integrationCode);
    console.log('✅ Code d\'intégration généré: reflection_interface_integration.js');
    
    // Générer le rapport
    const reportPath = reflectionSystem.generateSystemReport();
    console.log(`📊 Rapport généré: ${reportPath}`);
    
    console.log('\n🎯 SYSTÈME DE QUESTIONNEMENT RÉFLEXIF PRÊT !');
    console.log('🔧 Intégrez le code dans votre interface JARVIS');
    console.log('🧠 L\'agent sera stimulé à réfléchir sur ses réponses');
}
