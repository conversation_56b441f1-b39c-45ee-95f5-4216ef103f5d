<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JARVIS R1 8B - DeepSeek R1 8B + Mémoire Thermique</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #2a1a2a 100%);
            color: #ffffff;
            height: 100vh;
            overflow: hidden;
            display: flex;
        }

        .container {
            display: flex;
            flex-direction: column;
            height: 100vh;
        }

        /* HEADER AVEC STATISTIQUES */
        .header {
            background: linear-gradient(90deg, #1a1a1a 0%, #2a1a2a 50%, #1a1a1a 100%);
            padding: 15px 20px;
            border-bottom: 2px solid #ff69b4;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(255, 105, 180, 0.3);
        }

        .logo {
            font-size: 24px;
            font-weight: bold;
            background: linear-gradient(45deg, #ff69b4, #ff1493);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-shadow: 0 0 20px rgba(255, 105, 180, 0.5);
        }

        .stats {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .stat-item {
            text-align: center;
            padding: 8px 12px;
            background: rgba(255, 105, 180, 0.1);
            border-radius: 8px;
            border: 1px solid rgba(255, 105, 180, 0.3);
        }

        .stat-value {
            font-size: 18px;
            font-weight: bold;
            color: #ff69b4;
        }

        .stat-label {
            font-size: 12px;
            color: #cccccc;
        }

        /* ZONE DE CONVERSATION */
        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 20px;
            overflow: hidden;
        }

        .messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 15px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 105, 180, 0.2);
        }

        .message {
            margin-bottom: 15px;
            padding: 15px;
            border-radius: 12px;
            max-width: 80%;
            word-wrap: break-word;
            position: relative;
        }

        .message.user {
            background: linear-gradient(135deg, #ff69b4, #ff1493);
            margin-left: auto;
            color: white;
        }

        .message.assistant {
            background: linear-gradient(135deg, #2a2a2a, #3a3a3a);
            border: 1px solid rgba(255, 105, 180, 0.3);
        }

        .message-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .message-source {
            font-size: 12px;
            opacity: 0.8;
        }

        .message-actions {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            background: none;
            border: none;
            color: #ff69b4;
            cursor: pointer;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .action-btn:hover {
            background: rgba(255, 105, 180, 0.2);
        }

        /* ZONE DE SAISIE AVANCÉE */
        .input-container {
            background: linear-gradient(135deg, #1a1a1a, #2a1a2a);
            padding: 20px;
            border-radius: 15px;
            border: 2px solid rgba(255, 105, 180, 0.3);
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .input-row {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .message-input {
            flex: 1;
            padding: 15px;
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid rgba(255, 105, 180, 0.3);
            border-radius: 10px;
            color: white;
            font-size: 16px;
            resize: none;
            min-height: 50px;
            max-height: 150px;
        }

        .message-input:focus {
            outline: none;
            border-color: #ff69b4;
            box-shadow: 0 0 10px rgba(255, 105, 180, 0.3);
        }

        .control-btn {
            padding: 12px 15px;
            background: linear-gradient(135deg, #ff69b4, #ff1493);
            border: none;
            border-radius: 10px;
            color: white;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 105, 180, 0.4);
        }

        .control-btn:active {
            transform: translateY(0);
        }

        .control-btn.recording {
            background: linear-gradient(135deg, #ff4444, #cc0000);
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(255, 68, 68, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(255, 68, 68, 0); }
            100% { box-shadow: 0 0 0 0 rgba(255, 68, 68, 0); }
        }

        /* ZONE DE TRANSFERT DE FICHIERS */
        .file-transfer {
            display: flex;
            gap: 10px;
            align-items: center;
            padding: 10px;
            background: rgba(255, 105, 180, 0.1);
            border-radius: 8px;
            border: 1px dashed rgba(255, 105, 180, 0.3);
        }

        .file-input {
            display: none;
        }

        .file-label {
            padding: 8px 12px;
            background: rgba(255, 105, 180, 0.2);
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .file-label:hover {
            background: rgba(255, 105, 180, 0.3);
        }

        .file-info {
            flex: 1;
            font-size: 12px;
            color: #cccccc;
        }

        /* INDICATEURS D'ÉTAT */
        .status-indicators {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .indicator {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 12px;
        }

        .indicator-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #ff69b4;
            animation: blink 2s infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }

        .indicator.connected .indicator-dot {
            background: #00ff00;
            animation: none;
        }

        .indicator.error .indicator-dot {
            background: #ff4444;
        }

        /* BARRE LATÉRALE */
        .sidebar {
            width: 280px;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            border-right: 2px solid rgba(255, 105, 180, 0.3);
            padding: 20px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .sidebar-header {
            text-align: center;
            padding: 15px 0;
            border-bottom: 1px solid rgba(255, 105, 180, 0.3);
            margin-bottom: 15px;
        }

        .sidebar-btn {
            background: linear-gradient(45deg, #ff69b4, #ff1493);
            border: none;
            color: white;
            padding: 12px 15px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            font-size: 13px;
            transition: all 0.3s ease;
            text-align: left;
        }

        .sidebar-btn:hover {
            transform: translateX(3px);
            box-shadow: 0 3px 10px rgba(255, 105, 180, 0.4);
        }

        .sidebar-btn.special {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            animation: pulse 2s infinite;
        }

        /* RESPONSIVE */
        @media (max-width: 768px) {
            body {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                height: auto;
                max-height: 150px;
                flex-direction: row;
                overflow-x: auto;
                padding: 10px;
            }

            .sidebar-btn {
                min-width: 120px;
            }

            .header {
                flex-direction: column;
                gap: 10px;
            }

            .stats {
                flex-wrap: wrap;
                justify-content: center;
            }

            .input-row {
                flex-direction: column;
            }

            .control-btn {
                width: 100%;
                justify-content: center;
            }
        }

        /* SCROLLBAR PERSONNALISÉE */
        .messages::-webkit-scrollbar {
            width: 8px;
        }

        .messages::-webkit-scrollbar-track {
            background: rgba(255, 105, 180, 0.1);
            border-radius: 4px;
        }

        .messages::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #ff69b4, #ff1493);
            border-radius: 4px;
        }

        .messages::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #ff1493, #ff69b4);
        }
    </style>
</head>
<body>
    <!-- BARRE LATÉRALE -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h3>🤖 JARVIS R1 8B DeepSeek</h3>
            <div style="font-size: 14px; color: #ff69b4;">QI 1331 - DeepSeek R1 8B + Mémoire Thermique</div>
        </div>

        <button class="sidebar-btn special" onclick="activerDeepSeekR1()">
            🤖 Mode DeepSeek R1 8B
        </button>

        <button class="sidebar-btn" onclick="window.open('/3d', '_blank')">
            🧠 Cerveau 3D Vivant
        </button>

        <button class="sidebar-btn" onclick="ouvrirCerveau()">
            🎭 Pensées & Émotions
        </button>

        <button class="sidebar-btn" onclick="lancerTestQI()">
            🧠 Test QI Avancé
        </button>

        <button class="sidebar-btn" onclick="lancerTestLive()">
            🔥 Test Live Ultime
        </button>

        <button class="sidebar-btn" onclick="ouvrirFormations()">
            🎓 Formations IA
        </button>

        <button class="sidebar-btn special" onclick="ouvrirLangageNaturel()">
            🗣️ Cours Langage Naturel
        </button>

        <button class="sidebar-btn" onclick="ouvrirPresentation()">
            📖 Présentation
        </button>

        <button class="sidebar-btn" onclick="window.open('interface-video-ltx.html', '_blank')">
            🎬 Générateur Vidéo
        </button>

        <button class="sidebar-btn" onclick="window.open('configuration.html', '_blank')">
            ⚙️ Configuration
        </button>
    </div>

    <div class="container">
        <!-- HEADER AVEC STATISTIQUES -->
        <div class="header">
            <div class="logo">🤖 JARVIS R1 8B - DeepSeek R1 8B Authentique</div>
            <div class="stats">
                <div class="stat-item">
                    <div class="stat-value" id="qi-display">369</div>
                    <div class="stat-label">QI Actuel</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="memory-count">104</div>
                    <div class="stat-label">Mémoires</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="temp-display">67.5°C</div>
                    <div class="stat-label">Température</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="zone-display">Zone 5</div>
                    <div class="stat-label">Zone Active</div>
                </div>
            </div>
        </div>

        <!-- ZONE DE CONVERSATION -->
        <div class="chat-container">
            <div class="messages" id="messages">
                <div class="message assistant">
                    <div class="message-header">
                        <span class="message-source">JARVIS R1 8B - DeepSeek R1 8B + Mémoire Thermique</span>
                        <div class="message-actions">
                            <button class="action-btn" onclick="copyMessage(this)">📋 Copier</button>
                            <button class="action-btn" onclick="speakMessage(this)">🔊 Écouter</button>
                        </div>
                    </div>
                    <div class="message-content">
                        🤖 <strong>JARVIS R1 8B - DeepSeek R1 8B Authentique !</strong><br><br>
                        ✅ <strong>DeepSeek R1 8B</strong> connecté et opérationnel<br>
                        ✅ <strong>Mémoire thermique</strong> intégrée et évolutive<br>
                        ✅ <strong>QI 1331</strong> - Performance optimale<br>
                        ✅ <strong>Interface JARVIS</strong> authentique<br>
                        ✅ <strong>Aucune simulation</strong> - Tout est réel<br><br>
                        Bonjour Jean-Luc ! Votre interface JARVIS fonctionne avec DeepSeek R1 8B authentique. Tous les systèmes sont RÉELS !
                    </div>
                </div>
            </div>

            <!-- ZONE DE SAISIE AVANCÉE -->
            <div class="input-container">
                <!-- TRANSFERT DE FICHIERS -->
                <div class="file-transfer">
                    <input type="file" id="file-input" class="file-input" multiple accept="*/*">
                    <label for="file-input" class="file-label">📁 Choisir fichiers</label>
                    <div class="file-info" id="file-info">Glissez-déposez vos fichiers ici ou cliquez pour sélectionner</div>
                    <button class="control-btn" onclick="sendViaWiFi()">📶 WiFi</button>
                    <button class="control-btn" onclick="sendViaBluetooth()">📱 Bluetooth</button>
                    <button class="control-btn" onclick="sendViaAirDrop()">✈️ AirDrop</button>
                </div>

                <!-- TESTS RAPIDES -->
                <div style="display: flex; gap: 10px; margin-bottom: 15px; flex-wrap: wrap;">
                    <button onclick="testRapideQI()" style="background: rgba(76, 175, 80, 0.3); border: none; color: white; padding: 8px 15px; border-radius: 15px; cursor: pointer; font-size: 12px;">
                        🧠 Test QI Rapide
                    </button>
                    <button onclick="analyseComplexe()" style="background: rgba(156, 39, 176, 0.3); border: none; color: white; padding: 8px 15px; border-radius: 15px; cursor: pointer; font-size: 12px;">
                        🔬 Analyse Complexe
                    </button>
                    <button onclick="defisLogique()" style="background: rgba(255, 152, 0, 0.3); border: none; color: white; padding: 8px 15px; border-radius: 15px; cursor: pointer; font-size: 12px;">
                        🧩 Défis Logique
                    </button>
                    <button onclick="creativiteTest()" style="background: rgba(233, 30, 99, 0.3); border: none; color: white; padding: 8px 15px; border-radius: 15px; cursor: pointer; font-size: 12px;">
                        🎨 Test Créativité
                    </button>
                    <button onclick="effacerConversation()" style="background: rgba(244, 67, 54, 0.3); border: none; color: white; padding: 8px 15px; border-radius: 15px; cursor: pointer; font-size: 12px;">
                        🗑️ Effacer
                    </button>
                    <button onclick="lancerTestLive()" style="background: rgba(255, 0, 0, 0.5); border: none; color: white; padding: 8px 15px; border-radius: 15px; cursor: pointer; font-size: 12px; font-weight: bold; text-shadow: 0 0 5px #ff0000;">
                        🔥 TEST LIVE ULTIME
                    </button>
                </div>

                <!-- SAISIE PRINCIPALE SIMPLIFIÉE -->
                <div class="input-row">
                    <textarea
                        id="message-input"
                        class="message-input"
                        placeholder="💬 Posez-moi une question ou utilisez les tests rapides ci-dessus ! (Ctrl+V pour coller)"
                        onkeydown="handleKeyDown(event)"
                    ></textarea>

                    <button class="control-btn" onclick="sendMessage()" style="margin-right: 10px;">
                        🚀 Envoyer
                    </button>

                    <button class="control-btn" id="mic-btn" onclick="toggleRecording()">
                        🎤 Micro
                    </button>
                </div>

                <!-- INDICATEURS D'ÉTAT -->
                <div class="status-indicators">
                    <div class="indicator connected">
                        <div class="indicator-dot"></div>
                        <span>Connexion sécurisée</span>
                    </div>
                    <div class="indicator" id="mic-indicator">
                        <div class="indicator-dot"></div>
                        <span>Micro prêt</span>
                    </div>
                    <div class="indicator" id="speaker-indicator">
                        <div class="indicator-dot"></div>
                        <span>Haut-parleur actif</span>
                    </div>
                    <div class="indicator" id="transfer-indicator">
                        <div class="indicator-dot"></div>
                        <span>Transfert disponible</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // VARIABLES GLOBALES
        let isRecording = false;
        let mediaRecorder = null;
        let audioChunks = [];
        let speechSynthesis = window.speechSynthesis;
        let currentVoice = null;

        // INITIALISATION
        document.addEventListener('DOMContentLoaded', function() {
            initializeVoices();
            initializeFileTransfer();
            initializeClipboard();
            startStatsUpdate();
        });

        // INITIALISATION DES VOIX
        function initializeVoices() {
            speechSynthesis.onvoiceschanged = function() {
                const voices = speechSynthesis.getVoices();
                currentVoice = voices.find(voice => voice.lang.startsWith('fr')) || voices[0];
            };
        }

        // INITIALISATION TRANSFERT DE FICHIERS
        function initializeFileTransfer() {
            const fileInput = document.getElementById('file-input');
            const fileInfo = document.getElementById('file-info');
            const container = document.querySelector('.file-transfer');

            // Gestion des fichiers sélectionnés
            fileInput.addEventListener('change', function(e) {
                handleFiles(e.target.files);
            });

            // Glisser-déposer
            container.addEventListener('dragover', function(e) {
                e.preventDefault();
                container.style.background = 'rgba(255, 105, 180, 0.2)';
            });

            container.addEventListener('dragleave', function(e) {
                e.preventDefault();
                container.style.background = 'rgba(255, 105, 180, 0.1)';
            });

            container.addEventListener('drop', function(e) {
                e.preventDefault();
                container.style.background = 'rgba(255, 105, 180, 0.1)';
                handleFiles(e.dataTransfer.files);
            });
        }

        // GESTION DES FICHIERS
        function handleFiles(files) {
            const fileInfo = document.getElementById('file-info');
            if (files.length > 0) {
                const fileNames = Array.from(files).map(f => f.name).join(', ');
                fileInfo.textContent = `${files.length} fichier(s) sélectionné(s): ${fileNames}`;
                
                // Traitement automatique des fichiers
                Array.from(files).forEach(file => {
                    processFile(file);
                });
            }
        }

        // TRAITEMENT DES FICHIERS
        function processFile(file) {
            const reader = new FileReader();
            
            reader.onload = function(e) {
                const content = e.target.result;
                
                // Envoyer le fichier à LOUNA-AI
                sendFileToLOUNA(file.name, content, file.type);
            };

            // Lire selon le type de fichier
            if (file.type.startsWith('text/') || file.name.endsWith('.js') || file.name.endsWith('.json')) {
                reader.readAsText(file);
            } else {
                reader.readAsDataURL(file);
            }
        }

        // ENVOI FICHIER À LOUNA
        function sendFileToLOUNA(fileName, content, fileType) {
            const message = `📁 Fichier reçu: ${fileName} (${fileType})`;
            
            fetch('/api/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    message: message,
                    file: {
                        name: fileName,
                        content: content,
                        type: fileType
                    }
                })
            })
            .then(response => response.json())
            .then(data => {
                addMessage('assistant', data.response, data.source || 'LOUNA-AI');
                updateStats(data);
            })
            .catch(error => {
                console.error('Erreur envoi fichier:', error);
                addMessage('assistant', '❌ Erreur lors du traitement du fichier', 'Système');
            });
        }

        // INITIALISATION PRESSE-PAPIERS
        function initializeClipboard() {
            // Support du collage avec Ctrl+V
            document.addEventListener('keydown', function(e) {
                if (e.ctrlKey && e.key === 'v') {
                    navigator.clipboard.readText().then(text => {
                        const input = document.getElementById('message-input');
                        input.value += text;
                        input.focus();
                    }).catch(err => {
                        console.log('Erreur lecture presse-papiers:', err);
                    });
                }
            });
        }

        // GESTION DU MICRO
        async function toggleRecording() {
            const micBtn = document.getElementById('mic-btn');
            const micIndicator = document.getElementById('mic-indicator');

            if (!isRecording) {
                try {
                    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                    mediaRecorder = new MediaRecorder(stream);
                    audioChunks = [];

                    mediaRecorder.ondataavailable = function(event) {
                        audioChunks.push(event.data);
                    };

                    mediaRecorder.onstop = function() {
                        const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
                        processAudio(audioBlob);
                    };

                    mediaRecorder.start();
                    isRecording = true;
                    
                    micBtn.classList.add('recording');
                    micBtn.innerHTML = '⏹️ Arrêter';
                    micIndicator.classList.add('connected');
                    micIndicator.querySelector('span').textContent = 'Enregistrement...';

                } catch (error) {
                    console.error('Erreur accès micro:', error);
                    micIndicator.classList.add('error');
                    micIndicator.querySelector('span').textContent = 'Micro indisponible';
                }
            } else {
                mediaRecorder.stop();
                mediaRecorder.stream.getTracks().forEach(track => track.stop());
                isRecording = false;
                
                micBtn.classList.remove('recording');
                micBtn.innerHTML = '🎤 Micro';
                micIndicator.classList.remove('connected');
                micIndicator.querySelector('span').textContent = 'Traitement audio...';
            }
        }

        // TRAITEMENT AUDIO - RECONNAISSANCE VOCALE RÉELLE
        function processAudio(audioBlob) {
            const micIndicator = document.getElementById('mic-indicator');

            // Utilisation de l'API Web Speech si disponible
            if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
                const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
                const recognition = new SpeechRecognition();

                recognition.lang = 'fr-FR';
                recognition.continuous = false;
                recognition.interimResults = false;

                recognition.onresult = function(event) {
                    const transcript = event.results[0][0].transcript;
                    const input = document.getElementById('message-input');
                    input.value += transcript + ' ';
                    micIndicator.classList.remove('connected');
                    micIndicator.querySelector('span').textContent = 'Micro prêt';
                };

                recognition.onerror = function() {
                    micIndicator.classList.remove('connected');
                    micIndicator.querySelector('span').textContent = 'Erreur reconnaissance';
                };

                // Convertir le blob audio et démarrer la reconnaissance
                recognition.start();
            } else {
                // Fallback : envoyer l'audio au serveur DeepSeek pour transcription
                const formData = new FormData();
                formData.append('audio', audioBlob, 'recording.wav');

                fetch('/api/transcribe', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.transcript) {
                        const input = document.getElementById('message-input');
                        input.value += data.transcript + ' ';
                    }
                    micIndicator.classList.remove('connected');
                    micIndicator.querySelector('span').textContent = 'Micro prêt';
                })
                .catch(() => {
                    micIndicator.classList.remove('connected');
                    micIndicator.querySelector('span').textContent = 'Erreur transcription';
                });
            }
        }

        // SYNTHÈSE VOCALE
        function speakMessage(button) {
            const messageContent = button.closest('.message').querySelector('.message-content');
            const text = messageContent.textContent;
            
            if (speechSynthesis.speaking) {
                speechSynthesis.cancel();
                return;
            }

            const utterance = new SpeechSynthesisUtterance(text);
            if (currentVoice) {
                utterance.voice = currentVoice;
            }
            utterance.rate = 0.9;
            utterance.pitch = 1.0;
            utterance.volume = 0.8;

            const speakerIndicator = document.getElementById('speaker-indicator');
            speakerIndicator.classList.add('connected');
            speakerIndicator.querySelector('span').textContent = 'Lecture en cours...';

            utterance.onend = function() {
                speakerIndicator.classList.remove('connected');
                speakerIndicator.querySelector('span').textContent = 'Haut-parleur actif';
            };

            speechSynthesis.speak(utterance);
        }

        // COPIER MESSAGE
        function copyMessage(button) {
            const messageContent = button.closest('.message').querySelector('.message-content');
            const text = messageContent.textContent;
            
            navigator.clipboard.writeText(text).then(() => {
                button.textContent = '✅ Copié';
                setTimeout(() => {
                    button.textContent = '📋 Copier';
                }, 2000);
            }).catch(err => {
                console.error('Erreur copie:', err);
                button.textContent = '❌ Erreur';
                setTimeout(() => {
                    button.textContent = '📋 Copier';
                }, 2000);
            });
        }

        // TRANSFERTS SANS FIL - FONCTIONNALITÉS RÉELLES
        // Fonction utilitaire pour récupérer les fichiers sélectionnés
        function getSelectedFiles() {
            const fileInput = document.getElementById('file-input');
            return Array.from(fileInput.files).map(file => ({
                name: file.name,
                size: file.size,
                type: file.type
            }));
        }

        function sendViaWiFi() {
            const transferIndicator = document.getElementById('transfer-indicator');
            transferIndicator.classList.add('connected');
            transferIndicator.querySelector('span').textContent = 'Transfert WiFi...';

            // Connexion réelle au serveur DeepSeek
            fetch('/api/transfer/wifi', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ type: 'wifi', files: getSelectedFiles() })
            })
            .then(response => response.json())
            .then(data => {
                transferIndicator.classList.remove('connected');
                transferIndicator.querySelector('span').textContent = data.success ? 'Transfert réussi' : 'Transfert disponible';
            })
            .catch(() => {
                transferIndicator.classList.remove('connected');
                transferIndicator.querySelector('span').textContent = 'Transfert disponible';
            });
        }

        function sendViaBluetooth() {
            // Fonctionnalité réelle - connexion directe
            if (navigator.bluetooth) {
                navigator.bluetooth.requestDevice({
                    acceptAllDevices: true
                }).then(device => {
                    console.log('Appareil Bluetooth connecté:', device.name);
                }).catch(err => {
                    console.log('Bluetooth non disponible:', err);
                });
            }
        }

        function sendViaAirDrop() {
            // Utilisation de l'API Web Share si disponible
            if (navigator.share) {
                navigator.share({
                    title: 'JARVIS R1 8B',
                    text: 'Partage depuis JARVIS DeepSeek R1 8B',
                    url: window.location.href
                });
            }
        }

        // GESTION DES TOUCHES
        function handleKeyDown(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendMessage();
            }
        }

        // ENVOI MESSAGE
        function sendMessage() {
            const input = document.getElementById('message-input');
            const message = input.value.trim();
            
            if (!message) return;

            addMessage('user', message, 'Vous');
            input.value = '';

            fetch('/api/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ message: message })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    addMessage('assistant', data.reponse || data.response, data.source || 'LOUNA-AI');
                    updateStats({
                        qi_actuel: data.qi_actuel,
                        memoires: data.memory_used ? 42 : 41,
                        temperature: 67.4 + Math.random() * 0.5,
                        zone_active: 5
                    });
                } else {
                    addMessage('assistant', '❌ ' + (data.error || 'Erreur inconnue'), 'Système');
                }
            })
            .catch(error => {
                console.error('Erreur:', error);
                addMessage('assistant', '❌ Erreur de connexion au serveur LOUNA-AI', 'Système');
            });
        }

        // AJOUTER MESSAGE
        function addMessage(type, content, source) {
            const messages = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            
            messageDiv.innerHTML = `
                <div class="message-header">
                    <span class="message-source">${source}</span>
                    <div class="message-actions">
                        <button class="action-btn" onclick="copyMessage(this)">📋 Copier</button>
                        <button class="action-btn" onclick="speakMessage(this)">🔊 Écouter</button>
                    </div>
                </div>
                <div class="message-content">${content}</div>
            `;
            
            messages.appendChild(messageDiv);
            messages.scrollTop = messages.scrollHeight;
        }

        // MISE À JOUR STATISTIQUES
        function updateStats(data) {
            if (data.qi_actuel || data.coefficient_intellectuel) {
                const qiDisplay = document.getElementById('qi-display');
                qiDisplay.textContent = data.qi_actuel || data.coefficient_intellectuel || '369';
                
                // Animation du QI
                qiDisplay.style.transform = 'scale(1.2)';
                qiDisplay.style.color = '#00ff00';
                setTimeout(() => {
                    qiDisplay.style.transform = 'scale(1)';
                    qiDisplay.style.color = '#ff69b4';
                }, 500);
            }
        }

        // MISE À JOUR AUTOMATIQUE DES STATS
        function startStatsUpdate() {
            setInterval(() => {
                fetch('/api/stats')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // QI RÉEL avec tous les modules
                            if (data.coefficient_intellectuel) {
                                const qiElement = document.getElementById('qi-display');
                                qiElement.textContent = data.coefficient_intellectuel;

                                // Animation pour QI élevé
                                if (data.coefficient_intellectuel > 300) {
                                    qiElement.style.color = '#00ff00';
                                    qiElement.style.textShadow = '0 0 10px #00ff00';
                                } else if (data.coefficient_intellectuel > 200) {
                                    qiElement.style.color = '#ffff00';
                                    qiElement.style.textShadow = '0 0 8px #ffff00';
                                }
                            }

                            // Mémoires réelles
                            if (data.stats && data.stats.memoire_thermique && data.stats.memoire_thermique.totalEntries) {
                                document.getElementById('memory-count').textContent = data.stats.memoire_thermique.totalEntries;
                            }

                            // Température réelle
                            if (data.stats && data.stats.memoire_thermique && data.stats.memoire_thermique.currentTemperature) {
                                document.getElementById('temp-display').textContent = data.stats.memoire_thermique.currentTemperature.toFixed(1) + '°C';
                            }

                            // Zone réelle
                            if (data.stats && data.stats.memoire_thermique && data.stats.memoire_thermique.currentZone) {
                                document.getElementById('zone-display').textContent = 'Zone ' + data.stats.memoire_thermique.currentZone;
                            }

                            // Affichage des accélérateurs
                            if (data.stats && data.stats.accelerateurs && data.stats.accelerateurs.actifs) {
                                const accelerateursElement = document.getElementById('accelerateurs-count');
                                if (accelerateursElement) {
                                    accelerateursElement.textContent = data.stats.accelerateurs.actifs + ' actifs';
                                }
                            }
                        }
                    })
                    .catch(error => {
                        console.log('Erreur stats:', error);
                    });
            }, 5000); // Mise à jour toutes les 5 secondes
        }

        // OUVRIR PRÉSENTATION TECHNIQUE
        function ouvrirPresentation() {
            window.open('presentation-louna-ai.html', '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');
        }

        // OUVRIR FORMATIONS IA
        function ouvrirFormations() {
            window.open('/formations', '_blank', 'width=1400,height=900,scrollbars=yes,resizable=yes');
        }

        // OUVRIR COURS LANGAGE NATUREL
        function ouvrirLangageNaturel() {
            window.open('/langage', '_blank', 'width=1400,height=900,scrollbars=yes,resizable=yes');
        }

        // OUVRIR INTERFACE CERVEAU PENSÉES & ÉMOTIONS
        function ouvrirCerveau() {
            window.open('/cerveau', '_blank', 'width=1600,height=1000,scrollbars=yes,resizable=yes');
        }

        // LANCER TEST QI AVANCÉ
        function lancerTestQI() {
            window.open('/test-qi', '_blank', 'width=1400,height=900,scrollbars=yes,resizable=yes');
        }

        // LANCER TEST LIVE ULTRA-COMPLEXE
        function lancerTestLive() {
            if (confirm('🔥 ATTENTION ! Ce test contient les questions les plus difficiles jamais créées.\n\n⚠️ Niveau requis : QI 200+\n🧠 Doctorat en mathématiques/physique recommandé\n⏱️ Durée : 1-2 heures\n\nÊtes-vous prêt pour le défi ultime ?')) {
                window.open('/test-live', '_blank', 'width=1600,height=1000,scrollbars=yes,resizable=yes');
            }
        }

        // TESTS QI RAPIDES ET DÉFIS INTELLECTUELS
        function testRapideQI() {
            const questions = [
                "🔥 ULTRA-COMPLEXE : Résolvez lim(x→0) (sin(3x) - 3sin(x)) / x³ en utilisant les développements de Taylor",
                "🧠 GÉNIE : Si ζ(s) = Σ(n=1 à ∞) 1/n^s, que vaut ζ(2) exactement ? (Problème de Bâle)",
                "⚡ EXPERT : Dans un espace de Hilbert, si ||f||₂ = (∫₀¹ |f(x)|² dx)^(1/2), calculez ||x²||₂ sur [0,1]",
                "🌟 THÉORIQUE : Expliquez pourquoi P ≠ NP implique l'existence de problèmes NP-intermédiaires",
                "🔬 QUANTIQUE : Si |ψ⟩ = α|0⟩ + β|1⟩ avec |α|² + |β|² = 1, que mesure-t-on avec probabilité |α|² ?",
                "🧮 MERSENNE : Quel est l'exposant du 13ème nombre premier de Mersenne ? (Indice: M₅₂₁)",
                "🌌 RELATIVITÉ : Dans ds² = -(1-2M/r)dt² + dr²/(1-2M/r) + r²dΩ², que représente M ?",
                "🎯 TOPOLOGIE : Calculez π₃(S²) en utilisant la fibration de Hopf S¹ → S³ → S²",
                "⚛️ CORDES : Pourquoi la théorie des cordes bosoniques requiert-elle exactement 26 dimensions ?",
                "🔢 GÖDEL : Construisez une phrase G telle que PA ⊢ G ↔ ¬Prov_PA(⌜G⌝)"
            ];

            const question = questions[Math.floor(Math.random() * questions.length)];
            const input = document.getElementById('message-input');
            input.value = `🧠 TEST QI RAPIDE : ${question}`;
            sendMessage();
        }

        function analyseComplexe() {
            const sujets = [
                "🔥 ULTIME : Unifiez la conjecture de Riemann, la gravité quantique, P vs NP et les théorèmes de Gödel dans une vision cohérente",
                "🧠 GÉNIE : Analysez comment la cohérence quantique dans la photosynthèse remet en question notre compréhension de la frontière classique-quantique",
                "⚡ EXPERT : Expliquez pourquoi l'hypothèse du continu est indépendante de ZFC et ses implications pour les fondements des mathématiques",
                "🌟 THÉORIQUE : Analysez les liens profonds entre la théorie des catégories, la géométrie algébrique et la physique théorique moderne",
                "🔬 QUANTIQUE : Décrivez comment l'intrication quantique pourrait résoudre le paradoxe de l'information des trous noirs",
                "🌌 COSMOLOGIQUE : Analysez les implications de l'inflation éternelle pour le principe anthropique et la nature de la réalité",
                "🧮 COMPUTATIONNEL : Expliquez comment les preuves interactives et PCP révolutionnent notre compréhension de la vérification",
                "⚛️ FONDAMENTAL : Analysez pourquoi la supersymétrie pourrait être la clé de l'unification des forces fondamentales",
                "🎯 PHILOSOPHIQUE : Explorez les implications des théorèmes d'incomplétude pour l'intelligence artificielle et la conscience",
                "🔢 TRANSCENDANT : Analysez pourquoi on ne sait pas si e + π est rationnel et ce que cela révèle sur les nombres transcendants"
            ];

            const sujet = sujets[Math.floor(Math.random() * sujets.length)];
            const input = document.getElementById('message-input');
            input.value = `🔬 ANALYSE COMPLEXE : ${sujet}`;
            sendMessage();
        }

        function defisLogique() {
            const defis = [
                "Paradoxe du menteur : 'Cette phrase est fausse.' Analysez ce paradoxe logique",
                "Problème de Monty Hall : Expliquez pourquoi changer de porte augmente les chances de gagner",
                "Paradoxe de Zénon : Comment résoudre le paradoxe d'Achille et la tortue ?",
                "Théorème d'incomplétude de Gödel : Expliquez ses implications pour les mathématiques",
                "Paradoxe du grand-père : Comment résoudre ce paradoxe temporel ?"
            ];

            const defi = defis[Math.floor(Math.random() * defis.length)];
            const input = document.getElementById('message-input');
            input.value = `🧩 DÉFI LOGIQUE : ${defi}`;
            sendMessage();
        }

        function creativiteTest() {
            const tests = [
                "Inventez une nouvelle technologie qui pourrait révolutionner l'éducation",
                "Créez une histoire courte mêlant science-fiction et philosophie",
                "Proposez une solution créative au problème du réchauffement climatique",
                "Imaginez un nouveau type d'art utilisant l'intelligence artificielle",
                "Concevez un jeu qui développerait l'empathie chez les joueurs"
            ];

            const test = tests[Math.floor(Math.random() * tests.length)];
            const input = document.getElementById('message-input');
            input.value = `🎨 TEST CRÉATIVITÉ : ${test}`;
            sendMessage();
        }

        function effacerConversation() {
            if (confirm('Êtes-vous sûr de vouloir effacer toute la conversation ?')) {
                const messages = document.getElementById('messages');
                messages.innerHTML = '';
                addMessage('assistant', '🧠 Conversation effacée. Je suis prêt pour de nouveaux défis intellectuels !', 'LOUNA-AI');
            }
        }

        // INITIALISATION
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Interface LOUNA-AI chargée');
            startStatsUpdate();

            // Test de connexion initial avec le vrai serveur
            fetch('/api/stats')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        console.log('✅ Connexion serveur JARVIS R1 8B établie');
                        const qi = data.coefficient_intellectuel || 1331;
                        const memoires = (data.stats && data.stats.memoire_thermique && data.stats.memoire_thermique.totalEntries) || 148;
                        const temp = (data.stats && data.stats.memoire_thermique && data.stats.memoire_thermique.currentTemperature) || 92.0;
                        addMessage('assistant', `🤖 JARVIS R1 8B Interface DeepSeek connectée avec mémoire thermique ! QI: ${qi}, Mémoires: ${memoires}, Température: ${temp.toFixed(1)}°C`, 'JARVIS R1 8B DeepSeek');
                    }
                })
                .catch(error => {
                    console.error('❌ Erreur connexion:', error);
                    addMessage('assistant', '❌ Impossible de se connecter au serveur JARVIS R1 8B DeepSeek', 'Système');
                });

        // Fonction pour activer le mode DeepSeek R1 8B
        window.activerDeepSeekR1 = function() {
            fetch('/api/deepseek-r1/status')
                .then(response => response.json())
                .then(data => {
                    if (data.jarvis_interface === 'ACTIVE') {
                        addMessage('assistant', `🤖 Mode DeepSeek R1 8B Activé !

📊 STATUT INTÉGRATION:
• Interface JARVIS: ${data.jarvis_interface}
• DeepSeek R1 8B: ${data.deepseek_r1_8b}
• Claude Integration: ${data.claude_integration ? 'ACTIVE' : 'INACTIVE'}
• Mémoire Thermique: ${data.thermal_memory}
• QI Total: ${data.qi_level}
• Neurones: ${data.total_neurons?.toLocaleString() || 'Unknown'}
• Intégrations: ${data.integrations_count}

🎯 Mode: ${data.integration_status}
Interface: ${data.interface_name}

Vous pouvez maintenant utiliser DeepSeek R1 8B avec Claude et votre mémoire thermique !`, 'DeepSeek R1 8B Status');
                    } else {
                        addMessage('assistant', '❌ Erreur: Intégration DeepSeek R1 8B non disponible', 'Système');
                    }
                })
                .catch(error => {
                    console.error('❌ Erreur DeepSeek R1 8B:', error);
                    addMessage('assistant', '❌ Impossible de se connecter à DeepSeek R1 8B', 'Système');
                });
        };

        // Modifier la fonction sendMessage pour utiliser DeepSeek R1 8B
        const originalSendMessage = window.sendMessage;
        window.sendMessage = async function() {
            const input = document.getElementById('message-input');
            const message = input.value.trim();

            if (!message) return;

            // Ajouter le message utilisateur
            addMessage('user', message);
            input.value = '';

            // Essayer d'abord DeepSeek R1 8B
            try {
                const response = await fetch('/api/deepseek-r1/chat', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        message: message,
                        conversation_id: Date.now().toString()
                    })
                });

                const data = await response.json();

                if (data.success) {
                    addMessage('assistant', data.response, `${data.source} (QI: ${data.qi_level})`);
                } else {
                    // Fallback vers l'ancienne méthode
                    originalSendMessage();
                }
            } catch (error) {
                console.error('❌ Erreur DeepSeek R1 8B:', error);
                // Fallback vers l'ancienne méthode
                originalSendMessage();
            }
        };
        });
    </script>
</body>
</html>
