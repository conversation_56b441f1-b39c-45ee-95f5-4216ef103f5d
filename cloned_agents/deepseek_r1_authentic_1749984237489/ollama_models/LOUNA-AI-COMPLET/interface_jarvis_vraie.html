<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎓 FORMATIONS SPÉCIALISÉES JARVIS</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: linear-gradient(135deg, #0c0c0c, #1a1a2e, #16213e);
            color: white;
            font-family: 'Courier New', monospace;
            min-height: 100vh;
            padding: 20px;
        }

        .formations-container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #4ecdc4, #44a08d);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(78, 205, 196, 0.3);
        }

        .title {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .subtitle {
            font-size: 16px;
            opacity: 0.9;
        }

        .formations-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .formation-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(78, 205, 196, 0.3);
            border-radius: 15px;
            padding: 25px;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .formation-card:hover {
            border-color: #4ecdc4;
            box-shadow: 0 10px 30px rgba(78, 205, 196, 0.2);
            transform: translateY(-5px);
        }

        .formation-title {
            color: #4ecdc4;
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 15px;
            text-align: center;
        }

        .formation-description {
            font-size: 14px;
            line-height: 1.6;
            margin-bottom: 20px;
            color: #b8b8b8;
        }

        .formation-content {
            background: rgba(78, 205, 196, 0.1);
            border: 1px solid rgba(78, 205, 196, 0.3);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            font-size: 13px;
        }

        .btn {
            background: linear-gradient(135deg, #4ecdc4, #44a08d);
            border: none;
            color: white;
            padding: 12px 25px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            width: 100%;
            transition: all 0.3s ease;
            font-family: 'Courier New', monospace;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(78, 205, 196, 0.4);
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .formation-status {
            text-align: center;
            padding: 20px;
            background: rgba(78, 205, 196, 0.1);
            border-radius: 15px;
            margin-bottom: 20px;
        }

        .progress-bar {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            height: 15px;
            margin: 15px 0;
            overflow: hidden;
        }

        .progress-fill {
            background: linear-gradient(90deg, #4ecdc4, #44a08d);
            height: 100%;
            width: 0%;
            transition: width 0.5s ease;
        }

        .formation-log {
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid rgba(78, 205, 196, 0.3);
            border-radius: 10px;
            padding: 15px;
            height: 200px;
            overflow-y: auto;
            font-size: 12px;
            margin-top: 20px;
        }

        .controls {
            text-align: center;
            margin: 20px 0;
        }

        .btn-large {
            padding: 15px 40px;
            font-size: 16px;
            margin: 0 10px;
        }

        .completed {
            border-color: #2ed573;
            background: rgba(46, 213, 115, 0.1);
        }

        .in-progress {
            border-color: #feca57;
            background: rgba(254, 202, 87, 0.1);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.8; }
        }
    </style>
</head>
<body>
    <div class="formations-container">
        <div class="header">
            <div class="title">🎓 FORMATIONS SPÉCIALISÉES JARVIS</div>
            <div class="subtitle">Correction des Lacunes • Amélioration des Performances • Créé par Jean-Luc PASSAVE</div>
        </div>

        <div class="formation-status" id="globalStatus">
            <h3>📊 STATUT GLOBAL FORMATIONS JARVIS</h3>
            <div id="globalText">🔄 Formations disponibles - Prêt à commencer</div>
            <div class="progress-bar">
                <div class="progress-fill" id="globalProgress"></div>
            </div>
            <div id="progressText">0% - Aucune formation complétée</div>
        </div>

        <div class="formations-grid">
            <!-- Formation 1: Logique Mathématique -->
            <div class="formation-card" id="formation1">
                <div class="formation-title">🔢 FORMATION LOGIQUE MATHÉMATIQUE</div>
                <div class="formation-description">
                    Correction des lacunes en calculs et suites logiques
                </div>
                <div class="formation-content">
                    <strong>📚 MODULES :</strong><br>
                    • Suites arithmétiques et géométriques<br>
                    • Progressions et formules<br>
                    • Calculs exponentiels (2^n)<br>
                    • Raisonnement numérique<br>
                    • Patterns mathématiques<br><br>
                    <strong>🎯 OBJECTIF :</strong> 100% réussite questions math
                </div>
                <button class="btn" onclick="startFormation(1)">🚀 DÉMARRER FORMATION</button>
            </div>

            <!-- Formation 2: Calcul Alphabétique -->
            <div class="formation-card" id="formation2">
                <div class="formation-title">🧮 FORMATION CALCUL ALPHABÉTIQUE</div>
                <div class="formation-description">
                    Maîtrise des conversions lettres-nombres
                </div>
                <div class="formation-content">
                    <strong>📚 MODULES :</strong><br>
                    • Valeurs alphabétiques (A=1, B=2...)<br>
                    • Calculs de mots (JARVIS, CLAUDE)<br>
                    • Encodage et décodage<br>
                    • Sommes alphabétiques<br>
                    • Vérifications croisées<br><br>
                    <strong>🎯 OBJECTIF :</strong> Calculs alphabétiques parfaits
                </div>
                <button class="btn" onclick="startFormation(2)">🚀 DÉMARRER FORMATION</button>
            </div>

            <!-- Formation 3: Classification -->
            <div class="formation-card" id="formation3">
                <div class="formation-title">📊 FORMATION CLASSIFICATION</div>
                <div class="formation-description">
                    Amélioration de l'identification et catégorisation
                </div>
                <div class="formation-content">
                    <strong>📚 MODULES :</strong><br>
                    • Catégories conceptuelles<br>
                    • Identification d'intrus<br>
                    • Groupements logiques<br>
                    • Domaines de connaissance<br>
                    • Analyse comparative<br><br>
                    <strong>🎯 OBJECTIF :</strong> Classification experte
                </div>
                <button class="btn" onclick="startFormation(3)">🚀 DÉMARRER FORMATION</button>
            </div>

            <!-- Formation 4: Logique Déductive -->
            <div class="formation-card" id="formation4">
                <div class="formation-title">🎯 FORMATION LOGIQUE DÉDUCTIVE</div>
                <div class="formation-description">
                    Perfectionnement du raisonnement logique
                </div>
                <div class="formation-content">
                    <strong>📚 MODULES :</strong><br>
                    • Syllogismes classiques<br>
                    • Raisonnement déductif<br>
                    • Inférences logiques<br>
                    • Prémisses et conclusions<br>
                    • Logique formelle<br><br>
                    <strong>🎯 OBJECTIF :</strong> Raisonnement parfait
                </div>
                <button class="btn" onclick="startFormation(4)">🚀 DÉMARRER FORMATION</button>
            </div>

            <!-- Formation 5: Géométrie Spatiale -->
            <div class="formation-card" id="formation5">
                <div class="formation-title">📐 FORMATION GÉOMÉTRIE SPATIALE</div>
                <div class="formation-description">
                    Amélioration de la visualisation 3D
                </div>
                <div class="formation-content">
                    <strong>📚 MODULES :</strong><br>
                    • Visualisation 3D<br>
                    • Calculs géométriques<br>
                    • Formes complexes<br>
                    • Relations spatiales<br>
                    • Triangles et polygones<br><br>
                    <strong>🎯 OBJECTIF :</strong> Maîtrise géométrie 3D
                </div>
                <button class="btn" onclick="startFormation(5)">🚀 DÉMARRER FORMATION</button>
            </div>

            <!-- Formation 6: Analogies -->
            <div class="formation-card" id="formation6">
                <div class="formation-title">🔗 FORMATION ANALOGIES</div>
                <div class="formation-description">
                    Perfectionnement des relations conceptuelles
                </div>
                <div class="formation-content">
                    <strong>📚 MODULES :</strong><br>
                    • Relations conceptuelles<br>
                    • Analogies complexes<br>
                    • Correspondances logiques<br>
                    • Patterns relationnels<br>
                    • Associations d'idées<br><br>
                    <strong>🎯 OBJECTIF :</strong> Analogies expertes
                </div>
                <button class="btn" onclick="startFormation(6)">🚀 DÉMARRER FORMATION</button>
            </div>
        </div>

        <div class="controls">
            <button class="btn btn-large" onclick="startAllFormations()">🚀 TOUTES LES FORMATIONS</button>
            <button class="btn btn-large" onclick="resetFormations()">🔄 RESET COMPLET</button>
            <button class="btn btn-large" onclick="testJarvisAfterFormation()">🧠 TESTER JARVIS FORMÉ</button>
        </div>

        <div class="formation-log" id="formationLog">
            🎓 FORMATIONS SPÉCIALISÉES JARVIS - LOG SYSTÈME<br>
            📚 6 formations disponibles pour corriger les lacunes<br>
            🎯 Objectif: Performance maximale au test QI<br>
            🔄 Prêt à commencer les formations...<br>
        </div>
    </div>

    <script>
        const formations = [
            {
                name: "Logique Mathématique",
                duration: 3000,
                modules: ["Suites", "Progressions", "Exponentielles", "Patterns", "Formules"]
            },
            {
                name: "Calcul Alphabétique", 
                duration: 2500,
                modules: ["Valeurs", "Conversions", "Encodage", "Sommes", "Vérifications"]
            },
            {
                name: "Classification",
                duration: 2000,
                modules: ["Catégories", "Intrus", "Groupements", "Domaines", "Comparaisons"]
            },
            {
                name: "Logique Déductive",
                duration: 3500,
                modules: ["Syllogismes", "Déduction", "Inférences", "Prémisses", "Logique"]
            },
            {
                name: "Géométrie Spatiale",
                duration: 4000,
                modules: ["3D", "Calculs", "Formes", "Relations", "Polygones"]
            },
            {
                name: "Analogies",
                duration: 2500,
                modules: ["Relations", "Correspondances", "Patterns", "Associations", "Concepts"]
            }
        ];

        let completedFormations = [];
        let currentFormation = null;

        // Démarrer une formation spécifique
        async function startFormation(formationId) {
            if (currentFormation) {
                addLog("⚠️ Formation en cours, veuillez patienter...");
                return;
            }

            const formation = formations[formationId - 1];
            currentFormation = formationId;
            
            addLog(`🚀 DÉMARRAGE: Formation ${formation.name}`);
            updateFormationCard(formationId, 'in-progress');
            
            // Simulation formation intensive
            for (let i = 0; i < formation.modules.length; i++) {
                const module = formation.modules[i];
                addLog(`📚 Module ${i + 1}/${formation.modules.length}: ${module}`);
                
                await new Promise(resolve => setTimeout(resolve, formation.duration / formation.modules.length));
                
                const progress = ((i + 1) / formation.modules.length) * 100;
                addLog(`✅ Module ${module} complété (${Math.round(progress)}%)`);
            }
            
            // Formation terminée
            completedFormations.push(formationId);
            updateFormationCard(formationId, 'completed');
            addLog(`🏆 FORMATION TERMINÉE: ${formation.name}`);
            
            // Sauvegarder les formations
            saveFormationProgress();
            updateGlobalProgress();
            
            currentFormation = null;
        }

        // Démarrer toutes les formations
        async function startAllFormations() {
            addLog("🚀 DÉMARRAGE: Toutes les formations");
            
            for (let i = 1; i <= formations.length; i++) {
                if (!completedFormations.includes(i)) {
                    await startFormation(i);
                    await new Promise(resolve => setTimeout(resolve, 500)); // Pause entre formations
                }
            }
            
            addLog("🏆 TOUTES LES FORMATIONS TERMINÉES !");
            addLog("🧠 JARVIS est maintenant EXPERT dans tous les domaines");
        }

        // Reset des formations
        function resetFormations() {
            completedFormations = [];
            currentFormation = null;
            
            // Reset visual
            for (let i = 1; i <= formations.length; i++) {
                updateFormationCard(i, 'default');
            }
            
            updateGlobalProgress();
            
            addLog("🔄 RESET: Toutes les formations réinitialisées");
            addLog("📚 Prêt pour nouvelles formations");
            
            // Clear storage
            localStorage.removeItem('jarvis_formations_completed');
        }

        // Tester JARVIS après formation
        function testJarvisAfterFormation() {
            const formationsCount = completedFormations.length;
            
            if (formationsCount === 0) {
                alert("⚠️ AUCUNE FORMATION COMPLÉTÉE\n\nJARVIS doit suivre au moins une formation avant le test.");
                return;
            }
            
            addLog(`🧠 LANCEMENT TEST: ${formationsCount} formations complétées`);
            
            if (formationsCount === formations.length) {
                addLog("🎯 JARVIS EXPERT: Performance maximale attendue");
                addLog("🏆 Score attendu: 10/10 - QI 145");
            } else {
                addLog(`📊 JARVIS PARTIELLEMENT FORMÉ: ${formationsCount}/${formations.length}`);
                addLog(`🎯 Performance améliorée attendue`);
            }
            
            // Ouvrir le test avec les formations
            window.open('jarvis_real_connection_qi_test.html', '_blank');
        }

        // Mettre à jour l'apparence d'une carte
        function updateFormationCard(formationId, status) {
            const card = document.getElementById(`formation${formationId}`);
            const btn = card.querySelector('.btn');
            
            card.className = 'formation-card';
            
            switch (status) {
                case 'completed':
                    card.classList.add('completed');
                    btn.textContent = '✅ FORMATION TERMINÉE';
                    btn.disabled = true;
                    break;
                case 'in-progress':
                    card.classList.add('in-progress');
                    btn.textContent = '🔄 FORMATION EN COURS...';
                    btn.disabled = true;
                    break;
                default:
                    btn.textContent = '🚀 DÉMARRER FORMATION';
                    btn.disabled = false;
            }
        }

        // Mettre à jour le progrès global
        function updateGlobalProgress() {
            const progress = (completedFormations.length / formations.length) * 100;
            document.getElementById('globalProgress').style.width = progress + '%';
            
            let statusText = '';
            if (progress === 0) {
                statusText = "🔄 Formations disponibles - Prêt à commencer";
            } else if (progress < 100) {
                statusText = `🔄 Formation en cours - ${completedFormations.length}/${formations.length} complétées`;
            } else {
                statusText = "🏆 TOUTES FORMATIONS TERMINÉES - JARVIS EXPERT";
            }
            
            document.getElementById('globalText').textContent = statusText;
            document.getElementById('progressText').textContent = 
                `${Math.round(progress)}% - ${getProgressDescription(progress)}`;
        }

        // Description du progrès
        function getProgressDescription(progress) {
            if (progress === 0) return "Aucune formation complétée";
            if (progress < 20) return "Formation logique mathématique";
            if (progress < 40) return "Formation calcul alphabétique";
            if (progress < 60) return "Formation classification";
            if (progress < 80) return "Formation logique déductive";
            if (progress < 100) return "Formation géométrie spatiale";
            return "JARVIS EXPERT - Toutes formations terminées";
        }

        // Sauvegarder le progrès
        function saveFormationProgress() {
            localStorage.setItem('jarvis_formations_completed', JSON.stringify(completedFormations));
            localStorage.setItem('jarvis_formation_level', completedFormations.length.toString());
        }

        // Charger le progrès
        function loadFormationProgress() {
            const saved = localStorage.getItem('jarvis_formations_completed');
            if (saved) {
                completedFormations = JSON.parse(saved);
                
                // Restaurer l'état visuel
                completedFormations.forEach(id => {
                    updateFormationCard(id, 'completed');
                });
                
                updateGlobalProgress();
                addLog(`✅ Formations restaurées: ${completedFormations.length}/${formations.length}`);
            }
        }

        // Ajouter au log
        function addLog(message) {
            const log = document.getElementById('formationLog');
            const timestamp = new Date().toLocaleTimeString();
            log.innerHTML += `[${timestamp}] ${message}<br>`;
            log.scrollTop = log.scrollHeight;
        }

        // Initialiser
        window.onload = function() {
            addLog('🎓 FORMATIONS SPÉCIALISÉES JARVIS INITIALISÉES');
            loadFormationProgress();
            console.log('🎓 FORMATIONS SPÉCIALISÉES JARVIS PRÊTES');
        };
    </script>
</body>
</html>
