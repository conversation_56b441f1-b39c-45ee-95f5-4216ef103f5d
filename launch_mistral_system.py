#!/usr/bin/env python3
"""
LANCEUR SYSTÈME MISTRAL 7B + MÉMOIRE THERMIQUE
Pipeline intégré selon l'ordre de Jean-Luc
Développé pour Jean-Luc PASSAVE - 2025
"""

import os
import sys
import subprocess
import time
import json
import sqlite3
from datetime import datetime

def check_dependencies():
    """Vérifie les dépendances requises"""
    print("🔍 Vérification des dépendances...")
    
    required_modules = [
        'flask', 'flask_cors', 'requests', 'sqlite3'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            if module == 'flask_cors':
                __import__('flask_cors')
            else:
                __import__(module)
            print(f"  ✅ {module}")
        except ImportError:
            missing_modules.append(module)
            print(f"  ❌ {module} - MANQUANT")
    
    if missing_modules:
        print(f"\n⚠️ Modules manquants: {', '.join(missing_modules)}")
        print("📦 Installation automatique...")
        
        for module in missing_modules:
            install_name = 'Flask-CORS' if module == 'flask_cors' else module
            try:
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', install_name])
                print(f"  ✅ {module} installé")
            except subprocess.CalledProcessError:
                print(f"  ❌ Erreur installation {module}")
                return False
    
    return True

def check_files():
    """Vérifie la présence des fichiers requis"""
    print("\n📁 Vérification des fichiers...")
    
    required_files = [
        'mistral_thermal_integration.py',
        'mistral_pipeline_integration.py',
        'mistral_web_interface.html',
        'mistral_server.py'
    ]
    
    missing_files = []
    
    for file in required_files:
        if os.path.exists(file):
            print(f"  ✅ {file}")
        else:
            missing_files.append(file)
            print(f"  ❌ {file} - MANQUANT")
    
    if missing_files:
        print(f"\n⚠️ Fichiers manquants: {', '.join(missing_files)}")
        return False
    
    return True

def init_thermal_database():
    """Initialise la base de données thermique avec données de base"""
    print("\n🌡️ Initialisation mémoire thermique...")
    
    db_path = "mistral_thermal.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Créer les tables si elles n'existent pas
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS thermal_memory (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                key TEXT UNIQUE,
                value TEXT,
                type TEXT,
                importance INTEGER DEFAULT 5,
                injection_time TEXT,
                last_accessed TEXT,
                access_count INTEGER DEFAULT 0
            )
        ''')
        
        # Données de base Jean-Luc
        base_data = {
            "identity": "Je suis Mistral avec mémoire thermique intégrée, créé par Jean-Luc PASSAVE",
            "creator": "Jean-Luc PASSAVE",
            "qi_level": 1131,
            "mission": "Assister Jean-Luc avec intelligence évolutive et mémoire persistante",
            "thermal_status": "active",
            "capabilities": [
                "Mémoire thermique persistante",
                "Évolution continue", 
                "Apprentissage contextuel",
                "Pipeline métacognitif"
            ],
            "pipeline_order": "Réflexion → Mémoire thermique → Internet → Apprentissage"
        }
        
        for key, value in base_data.items():
            cursor.execute('''
                INSERT OR REPLACE INTO thermal_memory 
                (key, value, type, importance, injection_time, last_accessed, access_count)
                VALUES (?, ?, ?, ?, ?, ?, 0)
            ''', (key, json.dumps(value), "core", 10, 
                  datetime.now().isoformat(), datetime.now().isoformat()))
        
        conn.commit()
        conn.close()
        
        print("  ✅ Base de données thermique initialisée")
        return True
        
    except Exception as e:
        print(f"  ❌ Erreur initialisation DB: {e}")
        return False

def test_system():
    """Test rapide du système"""
    print("\n🧪 Test du système...")
    
    try:
        # Test import des modules
        from mistral_thermal_integration import MistralThermalMemory
        from mistral_pipeline_integration import MistralPipelineSystem
        
        print("  ✅ Imports réussis")
        
        # Test création instance
        thermal = MistralThermalMemory()
        print("  ✅ Mémoire thermique créée")
        
        pipeline = MistralPipelineSystem()
        print("  ✅ Pipeline créé")
        
        # Test requête simple
        result = pipeline.process_pipeline("Test système")
        print("  ✅ Pipeline fonctionnel")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Erreur test: {e}")
        return False

def launch_server():
    """Lance le serveur Mistral"""
    print("\n🚀 Lancement du serveur Mistral...")
    
    try:
        # Lancer le serveur en arrière-plan
        process = subprocess.Popen([
            sys.executable, 'mistral_server.py'
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # Attendre un peu pour voir si le serveur démarre
        time.sleep(3)
        
        if process.poll() is None:
            print("  ✅ Serveur démarré avec succès")
            print("  🌐 Interface: http://localhost:8080")
            print("  📡 API: http://localhost:8080/api/")
            return process
        else:
            stdout, stderr = process.communicate()
            print(f"  ❌ Erreur démarrage serveur:")
            print(f"     STDOUT: {stdout.decode()}")
            print(f"     STDERR: {stderr.decode()}")
            return None
            
    except Exception as e:
        print(f"  ❌ Erreur lancement: {e}")
        return None

def show_usage_info():
    """Affiche les informations d'utilisation"""
    print("\n" + "="*80)
    print("🎯 SYSTÈME MISTRAL 7B + MÉMOIRE THERMIQUE OPÉRATIONNEL")
    print("="*80)
    print("👨‍💻 Développé pour Jean-Luc PASSAVE")
    print("📋 Pipeline: Réflexion → Mémoire thermique → Internet → Apprentissage")
    print()
    print("🌐 ACCÈS:")
    print("  • Interface Web: http://localhost:8080")
    print("  • API Chat:      http://localhost:8080/api/chat")
    print("  • Statut:        http://localhost:8080/api/status")
    print("  • Mémoire:       http://localhost:8080/api/thermal")
    print()
    print("💡 UTILISATION:")
    print("  1. Ouvrez http://localhost:8080 dans votre navigateur")
    print("  2. Posez vos questions à Mistral 7B")
    print("  3. Observez le pipeline en action")
    print("  4. La mémoire thermique s'enrichit automatiquement")
    print()
    print("🔧 COMMANDES RAPIDES:")
    print("  • 'Qui êtes-vous ?' - Test identité")
    print("  • 'Quel est votre QI ?' - Test mémoire")
    print("  • 'Parlez-moi de MCP' - Test formations")
    print("  • 'Comment fonctionne X ?' - Test pipeline complet")
    print()
    print("⚡ FONCTIONNALITÉS:")
    print("  ✓ Mémoire thermique persistante")
    print("  ✓ Pipeline métacognitif 4 étapes")
    print("  ✓ Apprentissage contextuel")
    print("  ✓ Formations MCP intégrées")
    print("  ✓ Interface web interactive")
    print("="*80)

def main():
    """Fonction principale de lancement"""
    print("🚀 LANCEUR SYSTÈME MISTRAL 7B + MÉMOIRE THERMIQUE")
    print("👨‍💻 Développé pour Jean-Luc PASSAVE - 2025")
    print("="*80)
    
    # Étape 1: Vérifier les dépendances
    if not check_dependencies():
        print("❌ Échec vérification dépendances")
        return False
    
    # Étape 2: Vérifier les fichiers
    if not check_files():
        print("❌ Fichiers manquants")
        return False
    
    # Étape 3: Initialiser la base de données
    if not init_thermal_database():
        print("❌ Échec initialisation DB")
        return False
    
    # Étape 4: Tester le système
    if not test_system():
        print("❌ Échec test système")
        return False
    
    # Étape 5: Lancer le serveur
    server_process = launch_server()
    if not server_process:
        print("❌ Échec lancement serveur")
        return False
    
    # Afficher les informations d'utilisation
    show_usage_info()
    
    try:
        print("\n⌨️ Appuyez sur Ctrl+C pour arrêter le serveur")
        server_process.wait()
    except KeyboardInterrupt:
        print("\n🛑 Arrêt du serveur...")
        server_process.terminate()
        server_process.wait()
        print("✅ Serveur arrêté")
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("🎯 Système Mistral terminé avec succès")
    else:
        print("❌ Erreur lors du lancement")
        sys.exit(1)
