#!/usr/bin/env python3
"""
PIPELINE MISTRAL 7B INTÉGRÉ - ORDRE JEAN-LUC
Réflexion → Mémoire thermique → Internet → Apprentissage
Développé pour Jean-Luc PASSAVE - 2025
"""

import json
import time
import sqlite3
import subprocess
import os
import requests
from datetime import datetime
from mistral_thermal_integration import MistralThermalMemory

class MistralPipelineSystem:
    """Système pipeline Mistral avec ordre spécifique de Jean-<PERSON>"""
    
    def __init__(self):
        # Initialiser la mémoire thermique existante
        self.thermal_memory = MistralThermalMemory()
        
        # Configuration pipeline
        self.pipeline_config = {
            "step_1": "reflection",
            "step_2": "thermal_memory", 
            "step_3": "internet",
            "step_4": "learning"
        }
        
        # Compteurs de performance
        self.pipeline_stats = {
            "total_queries": 0,
            "reflection_time": 0,
            "thermal_time": 0,
            "internet_time": 0,
            "learning_time": 0
        }
        
        print("🚀 Pipeline Mistral 7B + Mémoire Thermique initialisé")
        print(f"📋 Ordre: {' → '.join(self.pipeline_config.values())}")

    def step_1_reflection(self, user_input):
        """ÉTAPE 1: RÉFLEXION - Analyse métacognitive initiale"""
        start_time = time.time()
        
        print("🧠 ÉTAPE 1: Réflexion et analyse métacognitive...")
        
        # Analyse de la question
        question_analysis = self.analyze_question_depth(user_input)
        
        # Auto-questionnement
        self_questions = self.generate_reflection_questions(user_input)
        
        # Stratégie de réponse
        response_strategy = self.determine_response_strategy(user_input, question_analysis)
        
        reflection_result = {
            "question_type": question_analysis["type"],
            "complexity_level": question_analysis["complexity"],
            "domain": question_analysis["domain"],
            "self_questions": self_questions,
            "strategy": response_strategy,
            "requires_memory": question_analysis["needs_memory"],
            "requires_internet": question_analysis["needs_internet"],
            "processing_approach": question_analysis["approach"]
        }
        
        self.pipeline_stats["reflection_time"] += time.time() - start_time
        
        print(f"   ✓ Type: {reflection_result['question_type']}")
        print(f"   ✓ Complexité: {reflection_result['complexity_level']}/10")
        print(f"   ✓ Stratégie: {reflection_result['strategy']}")
        
        return reflection_result

    def step_2_thermal_memory(self, user_input, reflection):
        """ÉTAPE 2: MÉMOIRE THERMIQUE - Consultation contextuelle"""
        start_time = time.time()
        
        print("🌡️ ÉTAPE 2: Consultation mémoire thermique...")
        
        thermal_context = None
        
        if reflection["requires_memory"]:
            # Recherche ciblée selon le type de question
            if reflection["question_type"] == "identity":
                thermal_context = self.get_identity_context()
            elif reflection["question_type"] == "technical":
                thermal_context = self.get_technical_context(user_input)
            elif reflection["question_type"] == "learning":
                thermal_context = self.get_learning_context(user_input)
            else:
                thermal_context = self.thermal_memory.search_in_thermal_memory(user_input)
        
        self.pipeline_stats["thermal_time"] += time.time() - start_time
        
        if thermal_context:
            print(f"   ✓ Contexte trouvé: {str(thermal_context)[:100]}...")
        else:
            print("   ⚠️ Aucun contexte thermique pertinent")
        
        return thermal_context

    def step_3_internet(self, user_input, reflection, thermal_context):
        """ÉTAPE 3: INTERNET - Recherche si nécessaire"""
        start_time = time.time()
        
        print("🌐 ÉTAPE 3: Recherche internet si nécessaire...")
        
        internet_result = None
        
        # Recherche internet seulement si nécessaire et pas de contexte thermique
        if reflection["requires_internet"] and not thermal_context:
            print("   🔍 Recherche internet activée...")
            
            # Recherche MCP avancée
            internet_result = self.thermal_memory.search_mcp_mode(user_input)
            
            if not internet_result:
                # Recherche internet classique
                internet_result = self.thermal_memory.search_internet(user_input)
            
            if not internet_result:
                # Recherche fichiers locaux
                internet_result = self.thermal_memory.search_desktop_files(user_input)
        
        self.pipeline_stats["internet_time"] += time.time() - start_time
        
        if internet_result:
            print(f"   ✓ Résultat internet: {str(internet_result)[:100]}...")
        else:
            print("   ⚠️ Aucune recherche internet nécessaire")
        
        return internet_result

    def step_4_learning(self, user_input, reflection, thermal_context, internet_context):
        """ÉTAPE 4: APPRENTISSAGE - Génération avec Mistral 7B"""
        start_time = time.time()
        
        print("📚 ÉTAPE 4: Génération avec apprentissage Mistral 7B...")
        
        # Construire le prompt final intégré
        final_prompt = self.build_integrated_prompt(
            user_input, reflection, thermal_context, internet_context
        )
        
        # Génération avec Mistral (utiliser la logique existante)
        mistral_response = self.thermal_memory.query_mistral(user_input)
        
        # Apprentissage et mémorisation
        self.learn_from_pipeline(user_input, reflection, mistral_response)
        
        self.pipeline_stats["learning_time"] += time.time() - start_time
        
        print(f"   ✓ Réponse générée: {len(mistral_response)} caractères")
        
        return mistral_response

    def analyze_question_depth(self, question):
        """Analyse approfondie de la question"""
        question_lower = question.lower()
        
        # Déterminer le type
        if any(word in question_lower for word in ["qui êtes-vous", "identité", "créateur", "mission"]):
            q_type = "identity"
            needs_memory = True
            needs_internet = False
            approach = "direct_memory"
        elif any(word in question_lower for word in ["mcp", "cybersécurité", "commandes", "système"]):
            q_type = "technical"
            needs_memory = True
            needs_internet = False
            approach = "technical_lookup"
        elif any(word in question_lower for word in ["qu'est-ce", "comment", "pourquoi", "expliquer"]):
            q_type = "general"
            needs_memory = False
            needs_internet = True
            approach = "knowledge_synthesis"
        else:
            q_type = "unknown"
            needs_memory = True
            needs_internet = True
            approach = "comprehensive_search"
        
        # Calculer la complexité
        complexity = 1
        if len(question.split()) > 10: complexity += 2
        if any(word in question_lower for word in ["relation", "entre", "connexion"]): complexity += 3
        if any(word in question_lower for word in ["théorie", "principe", "mécanisme"]): complexity += 2
        
        # Déterminer le domaine
        if any(word in question_lower for word in ["science", "physique", "biologie"]): domain = "science"
        elif any(word in question_lower for word in ["informatique", "code", "programmation"]): domain = "tech"
        elif any(word in question_lower for word in ["philosophie", "éthique", "conscience"]): domain = "philosophy"
        else: domain = "general"
        
        return {
            "type": q_type,
            "complexity": min(10, complexity),
            "domain": domain,
            "needs_memory": needs_memory,
            "needs_internet": needs_internet,
            "approach": approach
        }

    def generate_reflection_questions(self, question):
        """Génère des questions de réflexion spécifiques"""
        question_lower = question.lower()
        
        if "identité" in question_lower or "qui êtes-vous" in question_lower:
            return ["Quels aspects de mon identité sont les plus pertinents ?"]
        elif "comment" in question_lower:
            return ["Quelle séquence logique sera la plus claire ?"]
        elif "pourquoi" in question_lower:
            return ["Quelles sont les causes fondamentales ?"]
        else:
            return ["Quelle approche sera la plus efficace ?"]

    def determine_response_strategy(self, question, analysis):
        """Détermine la stratégie de réponse optimale"""
        if analysis["type"] == "identity":
            return "consultation_mémoire_directe"
        elif analysis["complexity"] > 7:
            return "analyse_multi_niveaux"
        elif analysis["needs_internet"]:
            return "recherche_puis_synthèse"
        else:
            return "réponse_contextuelle"

    def get_identity_context(self):
        """Récupère le contexte d'identité depuis la mémoire thermique"""
        identity = self.thermal_memory.get_thermal_memory("identity")
        creator = self.thermal_memory.get_thermal_memory("creator")
        mission = self.thermal_memory.get_thermal_memory("mission")
        qi_level = self.thermal_memory.get_thermal_memory("qi_level")
        
        return {
            "identity": identity,
            "creator": creator,
            "mission": mission,
            "qi_level": qi_level
        }

    def get_technical_context(self, question):
        """Récupère le contexte technique"""
        if "mcp" in question.lower():
            return self.thermal_memory.get_thermal_memory("mcp_training")
        elif "cybersécurité" in question.lower():
            return self.thermal_memory.get_thermal_memory("security_training")
        else:
            return self.thermal_memory.get_thermal_memory("system_commands")

    def get_learning_context(self, question):
        """Récupère le contexte d'apprentissage"""
        return self.thermal_memory.search_in_thermal_memory(question)

    def build_integrated_prompt(self, user_input, reflection, thermal_context, internet_context):
        """Construit le prompt final intégré"""
        prompt_parts = []
        
        # Réflexion
        prompt_parts.append(f"RÉFLEXION: {reflection['strategy']}")
        
        # Contexte thermique
        if thermal_context:
            prompt_parts.append(f"MÉMOIRE: {str(thermal_context)[:200]}")
        
        # Contexte internet
        if internet_context:
            prompt_parts.append(f"RECHERCHE: {str(internet_context)[:200]}")
        
        # Question
        prompt_parts.append(f"QUESTION: {user_input}")
        
        return "\n".join(prompt_parts)

    def learn_from_pipeline(self, user_input, reflection, response):
        """Apprentissage depuis le pipeline complet"""
        # Stocker le pattern de réflexion
        pattern_key = f"pipeline_pattern_{int(time.time())}"
        pattern_data = {
            "question": user_input,
            "reflection_type": reflection["question_type"],
            "strategy_used": reflection["strategy"],
            "response_length": len(response),
            "timestamp": datetime.now().isoformat()
        }
        
        self.thermal_memory.store_thermal_memory(pattern_key, pattern_data, "pipeline", 6)

    def process_pipeline(self, user_input):
        """Traite la requête avec le pipeline complet"""
        start_time = time.time()
        self.pipeline_stats["total_queries"] += 1
        
        print(f"\n🚀 PIPELINE MISTRAL - Requête #{self.pipeline_stats['total_queries']}")
        print(f"📝 Question: {user_input}")
        print("=" * 60)
        
        # ÉTAPE 1: RÉFLEXION
        reflection = self.step_1_reflection(user_input)
        
        # ÉTAPE 2: MÉMOIRE THERMIQUE
        thermal_context = self.step_2_thermal_memory(user_input, reflection)
        
        # ÉTAPE 3: INTERNET
        internet_context = self.step_3_internet(user_input, reflection, thermal_context)
        
        # ÉTAPE 4: APPRENTISSAGE
        final_response = self.step_4_learning(user_input, reflection, thermal_context, internet_context)
        
        total_time = time.time() - start_time
        
        print("=" * 60)
        print(f"✅ PIPELINE TERMINÉ en {total_time:.2f}s")
        print(f"🤖 RÉPONSE: {final_response}")
        
        return {
            "user_input": user_input,
            "reflection": reflection,
            "thermal_context": thermal_context,
            "internet_context": internet_context,
            "final_response": final_response,
            "processing_time": total_time,
            "pipeline_stats": self.pipeline_stats.copy()
        }

def main():
    """Test du pipeline intégré"""
    print("🚀 PIPELINE MISTRAL 7B + MÉMOIRE THERMIQUE")
    print("📋 Ordre: Réflexion → Mémoire thermique → Internet → Apprentissage")
    print("=" * 80)
    
    # Créer le système pipeline
    pipeline = MistralPipelineSystem()
    
    # Tests
    test_questions = [
        "Qui êtes-vous ?",
        "Comment fonctionne la photosynthèse ?",
        "Quelles sont vos capacités MCP ?",
        "Expliquez-moi la relation entre l'IA et la conscience"
    ]
    
    for question in test_questions:
        result = pipeline.process_pipeline(question)
        print("\n" + "="*80 + "\n")
    
    print("🎯 PIPELINE MISTRAL OPÉRATIONNEL !")

if __name__ == "__main__":
    main()
