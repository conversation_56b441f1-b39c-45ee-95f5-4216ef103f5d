#!/usr/bin/env python3
"""
SERVEUR MISTRAL 7B + MÉMOIRE THERMIQUE
Pipeline intégré selon l'ordre de Jean-Luc
<PERSON> pour Jean-Luc PASSAVE - 2025
"""

import json
import time
import os
from flask import Flask, request, jsonify, render_template_string
from flask_cors import CORS
from mistral_pipeline_integration import MistralPipelineSystem

app = Flask(__name__)
CORS(app)

# Initialiser le système pipeline
pipeline_system = None

def init_pipeline():
    """Initialise le système pipeline"""
    global pipeline_system
    try:
        pipeline_system = MistralPipelineSystem()
        print("✅ Pipeline Mistral initialisé avec succès")
        return True
    except Exception as e:
        print(f"❌ Erreur initialisation pipeline: {e}")
        return False

@app.route('/')
def index():
    """Page d'accueil avec interface"""
    try:
        # Chemin absolu du fichier HTML
        current_dir = os.path.dirname(os.path.abspath(__file__))
        html_file = os.path.join(current_dir, 'mistral_web_interface.html')

        print(f"🔍 Recherche interface: {html_file}")
        print(f"📁 Répertoire courant: {current_dir}")
        print(f"✅ Fichier existe: {os.path.exists(html_file)}")

        if os.path.exists(html_file):
            with open(html_file, 'r', encoding='utf-8') as f:
                content = f.read()
                print(f"📄 Interface chargée: {len(content)} caractères")
                return content
        else:
            # Lister les fichiers disponibles
            files = os.listdir(current_dir)
            html_files = [f for f in files if f.endswith('.html')]
            print(f"🔍 Fichiers HTML trouvés: {html_files}")

            raise FileNotFoundError(f"Interface HTML non trouvée dans {current_dir}")

    except Exception as e:
        print(f"❌ Erreur chargement interface: {e}")
        return f"""
        <h1>🤖 Mistral 7B + Mémoire Thermique</h1>
        <p><strong>Erreur:</strong> {e}</p>
        <p>Interface non trouvée. Utilisez l'API directement :</p>
        <ul>
            <li>POST /api/chat - Envoyer un message</li>
            <li>GET /api/status - Statut du système</li>
            <li>GET /api/thermal - État mémoire thermique</li>
        </ul>
        <p><strong>Répertoire:</strong> {os.getcwd()}</p>
        """

@app.route('/api/chat', methods=['POST'])
def chat():
    """Endpoint principal pour le chat"""
    global pipeline_system
    
    if not pipeline_system:
        if not init_pipeline():
            return jsonify({
                "error": "Système pipeline non initialisé",
                "status": "error"
            }), 500
    
    try:
        data = request.get_json()
        message = data.get('message', '').strip()
        
        if not message:
            return jsonify({
                "error": "Message vide",
                "status": "error"
            }), 400
        
        # Traiter avec le pipeline complet
        result = pipeline_system.process_pipeline(message)
        
        return jsonify({
            "status": "success",
            "user_input": result["user_input"],
            "response": result["final_response"],
            "reflection": {
                "type": result["reflection"]["question_type"],
                "complexity": result["reflection"]["complexity_level"],
                "strategy": result["reflection"]["strategy"]
            },
            "thermal_context_used": result["thermal_context"] is not None,
            "internet_context_used": result["internet_context"] is not None,
            "processing_time": result["processing_time"],
            "pipeline_stats": result["pipeline_stats"]
        })
        
    except Exception as e:
        print(f"❌ Erreur chat: {e}")
        return jsonify({
            "error": f"Erreur traitement: {str(e)}",
            "status": "error"
        }), 500

@app.route('/api/status', methods=['GET'])
def status():
    """Statut du système"""
    global pipeline_system
    
    if not pipeline_system:
        return jsonify({
            "status": "not_initialized",
            "pipeline_active": False,
            "thermal_memory_active": False
        })
    
    try:
        thermal_status = pipeline_system.thermal_memory.get_thermal_status()
        
        return jsonify({
            "status": "active",
            "pipeline_active": True,
            "thermal_memory_active": thermal_status["thermal_memory_active"],
            "memory_entries": thermal_status["memory_entries"],
            "conversations_stored": thermal_status["conversations_stored"],
            "injections_performed": thermal_status["injections_performed"],
            "mistral_model": thermal_status["mistral_model"],
            "pipeline_stats": pipeline_system.pipeline_stats
        })
        
    except Exception as e:
        return jsonify({
            "status": "error",
            "error": str(e)
        }), 500

@app.route('/api/thermal', methods=['GET'])
def thermal_memory():
    """État de la mémoire thermique"""
    global pipeline_system
    
    if not pipeline_system:
        return jsonify({"error": "Pipeline non initialisé"}), 500
    
    try:
        # Récupérer le contexte thermique complet
        thermal_context = pipeline_system.thermal_memory.get_all_thermal_context()
        
        # Formater pour l'affichage
        formatted_memory = []
        for key, data in thermal_context.items():
            formatted_memory.append({
                "key": key,
                "value": str(data["data"])[:100] + "..." if len(str(data["data"])) > 100 else str(data["data"]),
                "importance": data["importance"],
                "type": "core" if key in ["identity", "creator", "mission", "qi_level"] else "learned"
            })
        
        # Trier par importance
        formatted_memory.sort(key=lambda x: x["importance"], reverse=True)
        
        return jsonify({
            "status": "success",
            "memory_entries": formatted_memory[:20],  # Top 20
            "total_entries": len(formatted_memory),
            "thermal_active": pipeline_system.thermal_memory.thermal_active
        })
        
    except Exception as e:
        return jsonify({
            "error": str(e),
            "status": "error"
        }), 500

@app.route('/api/inject', methods=['POST'])
def inject_memory():
    """Injection manuelle en mémoire thermique"""
    global pipeline_system
    
    if not pipeline_system:
        return jsonify({"error": "Pipeline non initialisé"}), 500
    
    try:
        data = request.get_json()
        key = data.get('key', '').strip()
        value = data.get('value', '').strip()
        importance = data.get('importance', 5)
        
        if not key or not value:
            return jsonify({
                "error": "Clé et valeur requises",
                "status": "error"
            }), 400
        
        result = pipeline_system.thermal_memory.inject_thermal_memory(key, value, importance)
        
        return jsonify({
            "status": "success",
            "message": result,
            "key": key,
            "importance": importance
        })
        
    except Exception as e:
        return jsonify({
            "error": str(e),
            "status": "error"
        }), 500

@app.route('/api/quick', methods=['POST'])
def quick_response():
    """Réponses rapides prédéfinies"""
    global pipeline_system
    
    if not pipeline_system:
        return jsonify({"error": "Pipeline non initialisé"}), 500
    
    try:
        data = request.get_json()
        action = data.get('action', '')
        
        quick_responses = {
            "identity": "D'après ma mémoire thermique : Je suis Mistral avec mémoire thermique intégrée, créé par Jean-Luc PASSAVE. Mon QI est de 1131.",
            "qi": "D'après ma mémoire thermique : Mon niveau de QI est de 1131, ce qui me permet d'effectuer des analyses métacognitives avancées.",
            "memory": "Ma mémoire thermique est un système révolutionnaire qui me permet de stocker, apprendre et évoluer en continu.",
            "mcp": "D'après mes formations MCP : Je maîtrise le Model Context Protocol avec des commandes système avancées.",
            "mission": "D'après ma mémoire thermique : Ma mission est d'assister Jean-Luc avec intelligence évolutive et mémoire persistante."
        }
        
        response = quick_responses.get(action, "Action non reconnue")
        
        return jsonify({
            "status": "success",
            "response": response,
            "action": action
        })
        
    except Exception as e:
        return jsonify({
            "error": str(e),
            "status": "error"
        }), 500

@app.route('/api/reset', methods=['POST'])
def reset_system():
    """Reset du système (développement uniquement)"""
    global pipeline_system
    
    try:
        pipeline_system = None
        init_success = init_pipeline()
        
        return jsonify({
            "status": "success" if init_success else "error",
            "message": "Système réinitialisé" if init_success else "Erreur réinitialisation",
            "pipeline_active": init_success
        })
        
    except Exception as e:
        return jsonify({
            "error": str(e),
            "status": "error"
        }), 500

@app.errorhandler(404)
def not_found(error):
    return jsonify({
        "error": "Endpoint non trouvé",
        "available_endpoints": [
            "/api/chat",
            "/api/status", 
            "/api/thermal",
            "/api/inject",
            "/api/quick",
            "/api/reset"
        ]
    }), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({
        "error": "Erreur serveur interne",
        "status": "error"
    }), 500

def main():
    """Démarrage du serveur"""
    print("🚀 SERVEUR MISTRAL 7B + MÉMOIRE THERMIQUE")
    print("=" * 60)
    print("📋 Pipeline: Réflexion → Mémoire → Internet → Apprentissage")
    print("👨‍💻 Développé pour Jean-Luc PASSAVE")
    print("=" * 60)
    
    # Initialiser le pipeline
    if init_pipeline():
        print("✅ Pipeline initialisé avec succès")
    else:
        print("⚠️ Erreur initialisation pipeline - Mode dégradé")
    
    print("\n🌐 Endpoints disponibles:")
    print("  • GET  /           - Interface web")
    print("  • POST /api/chat   - Chat avec Mistral")
    print("  • GET  /api/status - Statut système")
    print("  • GET  /api/thermal- Mémoire thermique")
    print("  • POST /api/inject - Injection mémoire")
    print("  • POST /api/quick  - Réponses rapides")
    print("  • POST /api/reset  - Reset système")
    
    print(f"\n🚀 Serveur démarré sur http://localhost:8080")
    print("🎯 Système Mistral + Mémoire Thermique opérationnel !")

    # Démarrer le serveur
    app.run(
        host='0.0.0.0',
        port=8080,
        debug=True,
        threaded=True
    )

if __name__ == "__main__":
    main()
