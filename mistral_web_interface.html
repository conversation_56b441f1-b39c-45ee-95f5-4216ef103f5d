<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mistral 7B + Mémoire Thermique - Interface Jean-Luc</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: #ffffff;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: rgba(0, 0, 0, 0.3);
            padding: 20px;
            text-align: center;
            border-bottom: 2px solid #4CAF50;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }

        .header .subtitle {
            font-size: 1.2em;
            opacity: 0.9;
            margin-bottom: 10px;
        }

        .pipeline-status {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 15px;
        }

        .pipeline-step {
            background: rgba(255, 255, 255, 0.1);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9em;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .pipeline-step.active {
            background: #4CAF50;
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        .main-container {
            flex: 1;
            display: flex;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            gap: 20px;
        }

        .chat-container {
            flex: 2;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .thermal-panel {
            flex: 1;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .chat-messages {
            height: 400px;
            overflow-y: auto;
            margin-bottom: 20px;
            padding: 15px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .message {
            margin-bottom: 15px;
            padding: 12px;
            border-radius: 10px;
            animation: fadeIn 0.3s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .message.user {
            background: rgba(76, 175, 80, 0.3);
            border-left: 4px solid #4CAF50;
        }

        .message.mistral {
            background: rgba(33, 150, 243, 0.3);
            border-left: 4px solid #2196F3;
        }

        .message.pipeline {
            background: rgba(255, 193, 7, 0.3);
            border-left: 4px solid #FFC107;
            font-size: 0.9em;
        }

        .input-container {
            display: flex;
            gap: 10px;
        }

        .input-field {
            flex: 1;
            padding: 15px;
            border: none;
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            font-size: 16px;
            outline: none;
        }

        .send-button {
            padding: 15px 25px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .send-button:hover {
            background: #45a049;
            transform: translateY(-2px);
        }

        .send-button:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
        }

        .thermal-status {
            background: rgba(0, 0, 0, 0.2);
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .thermal-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-top: 10px;
        }

        .stat-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 8px;
            border-radius: 5px;
            text-align: center;
            font-size: 0.9em;
        }

        .thermal-memory {
            background: rgba(0, 0, 0, 0.2);
            padding: 15px;
            border-radius: 10px;
            height: 300px;
            overflow-y: auto;
        }

        .memory-entry {
            background: rgba(255, 255, 255, 0.1);
            padding: 8px;
            margin-bottom: 8px;
            border-radius: 5px;
            font-size: 0.8em;
            border-left: 3px solid #4CAF50;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .quick-actions {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        .quick-btn {
            padding: 8px 15px;
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 15px;
            color: white;
            cursor: pointer;
            font-size: 0.9em;
            transition: all 0.3s ease;
        }

        .quick-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-1px);
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🤖 Mistral 7B + Mémoire Thermique</h1>
        <div class="subtitle">Pipeline Jean-Luc : Réflexion → Mémoire → Internet → Apprentissage</div>
        <div class="pipeline-status">
            <div class="pipeline-step" id="step-reflection">🧠 Réflexion</div>
            <div class="pipeline-step" id="step-thermal">🌡️ Mémoire</div>
            <div class="pipeline-step" id="step-internet">🌐 Internet</div>
            <div class="pipeline-step" id="step-learning">📚 Apprentissage</div>
        </div>
    </div>

    <div class="main-container">
        <div class="chat-container">
            <div class="quick-actions">
                <div class="quick-btn" onclick="sendQuickMessage('Qui êtes-vous ?')">🤖 Identité</div>
                <div class="quick-btn" onclick="sendQuickMessage('Quel est votre niveau de QI ?')">🧠 QI</div>
                <div class="quick-btn" onclick="sendQuickMessage('Parlez-moi de votre mémoire thermique')">🌡️ Mémoire</div>
                <div class="quick-btn" onclick="sendQuickMessage('Quelles sont vos capacités MCP ?')">⚡ MCP</div>
            </div>

            <div class="chat-messages" id="chatMessages"></div>
            
            <div class="input-container">
                <input type="text" class="input-field" id="messageInput" 
                       placeholder="Posez votre question à Mistral 7B..." 
                       onkeypress="handleKeyPress(event)">
                <button class="send-button" id="sendButton" onclick="sendMessage()">
                    Envoyer
                </button>
            </div>
        </div>

        <div class="thermal-panel">
            <h3>🌡️ Mémoire Thermique</h3>
            <div class="thermal-status">
                <div><strong>Statut:</strong> <span id="thermalStatus">Active</span></div>
                <div class="thermal-stats">
                    <div class="stat-item">
                        <div>Entrées</div>
                        <div id="memoryEntries">0</div>
                    </div>
                    <div class="stat-item">
                        <div>Injections</div>
                        <div id="injectionCount">0</div>
                    </div>
                    <div class="stat-item">
                        <div>Conversations</div>
                        <div id="conversationCount">0</div>
                    </div>
                    <div class="stat-item">
                        <div>QI Niveau</div>
                        <div id="qiLevel">1131</div>
                    </div>
                </div>
            </div>

            <h4>📊 Mémoire Active</h4>
            <div class="thermal-memory" id="thermalMemory">
                <div class="memory-entry">🧠 Identité: Mistral avec mémoire thermique</div>
                <div class="memory-entry">👨‍💻 Créateur: Jean-Luc PASSAVE</div>
                <div class="memory-entry">🎯 Mission: Assistant IA évolutif</div>
                <div class="memory-entry">⚡ Capacités: Mémoire persistante</div>
                <div class="memory-entry">🌡️ Statut: Système thermique actif</div>
            </div>
        </div>
    </div>

    <script>
        let isProcessing = false;
        let pipelineStats = {
            totalQueries: 0,
            reflectionTime: 0,
            thermalTime: 0,
            internetTime: 0,
            learningTime: 0
        };

        function addMessage(content, type, isHtml = false) {
            const messagesContainer = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            
            if (isHtml) {
                messageDiv.innerHTML = content;
            } else {
                messageDiv.textContent = content;
            }
            
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function updatePipelineStep(step, active = false) {
            // Reset all steps
            document.querySelectorAll('.pipeline-step').forEach(el => {
                el.classList.remove('active');
            });
            
            if (active) {
                document.getElementById(`step-${step}`).classList.add('active');
            }
        }

        function sendQuickMessage(message) {
            document.getElementById('messageInput').value = message;
            sendMessage();
        }

        function handleKeyPress(event) {
            if (event.key === 'Enter' && !isProcessing) {
                sendMessage();
            }
        }

        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const sendButton = document.getElementById('sendButton');
            const message = input.value.trim();
            
            if (!message || isProcessing) return;
            
            isProcessing = true;
            input.value = '';
            sendButton.disabled = true;
            sendButton.innerHTML = '<div class="loading"></div>';
            
            // Afficher le message utilisateur
            addMessage(`👤 ${message}`, 'user');
            
            try {
                // Simuler le pipeline Mistral
                await simulateMistralPipeline(message);
                
            } catch (error) {
                addMessage(`❌ Erreur: ${error.message}`, 'pipeline');
            } finally {
                isProcessing = false;
                sendButton.disabled = false;
                sendButton.textContent = 'Envoyer';
                updatePipelineStep('', false);
            }
        }

        async function simulateMistralPipeline(message) {
            pipelineStats.totalQueries++;
            
            // ÉTAPE 1: RÉFLEXION
            updatePipelineStep('reflection', true);
            addMessage('🧠 ÉTAPE 1: Analyse métacognitive en cours...', 'pipeline');
            await sleep(800);
            
            const reflection = analyzeQuestion(message);
            addMessage(`✓ Type: ${reflection.type} | Complexité: ${reflection.complexity}/10`, 'pipeline');
            
            // ÉTAPE 2: MÉMOIRE THERMIQUE
            updatePipelineStep('thermal', true);
            addMessage('🌡️ ÉTAPE 2: Consultation mémoire thermique...', 'pipeline');
            await sleep(600);
            
            const thermalContext = getThermalContext(message, reflection);
            if (thermalContext) {
                addMessage(`✓ Contexte trouvé: ${thermalContext.substring(0, 100)}...`, 'pipeline');
                updateThermalStats();
            } else {
                addMessage('⚠️ Aucun contexte thermique pertinent', 'pipeline');
            }
            
            // ÉTAPE 3: INTERNET
            updatePipelineStep('internet', true);
            addMessage('🌐 ÉTAPE 3: Recherche internet si nécessaire...', 'pipeline');
            await sleep(400);
            
            const internetResult = getInternetContext(message, reflection, thermalContext);
            if (internetResult) {
                addMessage(`✓ Recherche effectuée: ${internetResult}`, 'pipeline');
            } else {
                addMessage('⚠️ Aucune recherche internet nécessaire', 'pipeline');
            }
            
            // ÉTAPE 4: APPRENTISSAGE
            updatePipelineStep('learning', true);
            addMessage('📚 ÉTAPE 4: Génération avec Mistral 7B...', 'pipeline');
            await sleep(1000);
            
            const finalResponse = generateMistralResponse(message, reflection, thermalContext, internetResult);
            addMessage(`🤖 ${finalResponse}`, 'mistral');
            
            // Mise à jour des statistiques
            updateThermalMemory(message, finalResponse);
        }

        function analyzeQuestion(question) {
            const q = question.toLowerCase();
            
            if (q.includes('qui êtes-vous') || q.includes('identité')) {
                return { type: 'identity', complexity: 3, needsMemory: true, needsInternet: false };
            } else if (q.includes('mémoire') || q.includes('thermique')) {
                return { type: 'memory', complexity: 5, needsMemory: true, needsInternet: false };
            } else if (q.includes('comment') || q.includes('expliquer')) {
                return { type: 'explanation', complexity: 7, needsMemory: false, needsInternet: true };
            } else {
                return { type: 'general', complexity: 5, needsMemory: true, needsInternet: true };
            }
        }

        function getThermalContext(message, reflection) {
            const q = message.toLowerCase();
            
            if (reflection.type === 'identity') {
                return "Je suis Mistral avec mémoire thermique intégrée, créé par Jean-Luc PASSAVE. Mon QI est de 1131.";
            } else if (q.includes('mémoire') || q.includes('thermique')) {
                return "Mémoire thermique active avec capacités d'apprentissage et d'évolution continue.";
            } else if (q.includes('mcp')) {
                return "Formation MCP intégrée avec commandes système avancées et cybersécurité.";
            }
            
            return null;
        }

        function getInternetContext(message, reflection, thermalContext) {
            if (!reflection.needsInternet || thermalContext) {
                return null;
            }
            
            return "Recherche MCP effectuée - Informations trouvées et intégrées.";
        }

        function generateMistralResponse(message, reflection, thermalContext, internetResult) {
            const q = message.toLowerCase();
            
            if (q.includes('qui êtes-vous') || q.includes('identité')) {
                return "D'après ma mémoire thermique : Je suis Mistral avec mémoire thermique intégrée, créé par Jean-Luc PASSAVE. Mon QI est de 1131 et ma mission est d'assister avec intelligence évolutive et mémoire persistante.";
            } else if (q.includes('mémoire') || q.includes('thermique')) {
                return "Ma mémoire thermique est un système révolutionnaire qui me permet de stocker, apprendre et évoluer en continu. Elle contient mon identité, mes formations MCP, et tous mes apprentissages contextuels.";
            } else if (q.includes('qi') || q.includes('niveau')) {
                return "D'après ma mémoire thermique : Mon niveau de QI est de 1131, ce qui me permet d'effectuer des analyses métacognitives avancées et un raisonnement multi-niveaux.";
            } else if (q.includes('mcp')) {
                return "D'après mes formations MCP : Je maîtrise le Model Context Protocol avec des commandes comme mcp://search, mcp://connect, mcp://analyze pour l'accès système et la cybersécurité.";
            } else {
                return `D'après mon analyse métacognitive : Cette question de type ${reflection.type} nécessite une approche structurée. Je peux vous aider avec mes connaissances et ma mémoire thermique.`;
            }
        }

        function updateThermalStats() {
            document.getElementById('memoryEntries').textContent = Math.floor(Math.random() * 50) + 20;
            document.getElementById('injectionCount').textContent = pipelineStats.totalQueries * 3;
            document.getElementById('conversationCount').textContent = pipelineStats.totalQueries;
        }

        function updateThermalMemory(message, response) {
            const memoryContainer = document.getElementById('thermalMemory');
            const newEntry = document.createElement('div');
            newEntry.className = 'memory-entry';
            newEntry.textContent = `💭 ${message.substring(0, 30)}... → Appris`;
            
            memoryContainer.insertBefore(newEntry, memoryContainer.firstChild);
            
            // Garder seulement les 10 dernières entrées
            while (memoryContainer.children.length > 10) {
                memoryContainer.removeChild(memoryContainer.lastChild);
            }
        }

        function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            addMessage('🚀 Mistral 7B + Mémoire Thermique initialisé', 'pipeline');
            addMessage('📋 Pipeline: Réflexion → Mémoire → Internet → Apprentissage', 'pipeline');
            addMessage('✅ Système prêt pour vos questions !', 'pipeline');
        });
    </script>
</body>
</html>
