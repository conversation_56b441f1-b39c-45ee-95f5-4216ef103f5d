
// 🧠 SYSTÈME DE QUESTIONNEMENT RÉFLEXIF INTÉGRÉ
class ReflectionInterface {
    constructor() {
        this.reflectionEnabled = true;
        this.setupReflectionUI();
    }
    
    setupReflectionUI() {
        // Ajouter le bouton de contrôle
        const controlPanel = document.querySelector('.sidebar') || document.body;
        
        const reflectionControl = document.createElement('div');
        reflectionControl.className = 'reflection-control';
        reflectionControl.innerHTML = `
            <div class="reflection-panel">
                <h4>🧠 Questionnement Réflexif</h4>
                <button id="toggle-reflection" class="reflection-btn active">
                    <span class="status">ACTIF</span>
                    <span class="icon">🔄</span>
                </button>
                <div class="reflection-stats">
                    <div>Réflexions: <span id="reflection-count">0</span></div>
                    <div>Statut: <span id="reflection-status">Activé</span></div>
                </div>
            </div>
        `;
        
        controlPanel.appendChild(reflectionControl);
        
        // Ajouter les styles
        const style = document.createElement('style');
        style.textContent = `
            .reflection-panel {
                background: linear-gradient(135deg, #1a1a2e, #16213e);
                border: 1px solid #ff69b4;
                border-radius: 10px;
                padding: 15px;
                margin: 10px 0;
                color: white;
            }
            
            .reflection-btn {
                background: linear-gradient(45deg, #ff69b4, #00d4ff);
                border: none;
                border-radius: 8px;
                padding: 10px 15px;
                color: white;
                cursor: pointer;
                width: 100%;
                display: flex;
                justify-content: space-between;
                align-items: center;
                font-weight: bold;
                transition: all 0.3s ease;
            }
            
            .reflection-btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 5px 15px rgba(255, 105, 180, 0.3);
            }
            
            .reflection-btn.inactive {
                background: linear-gradient(45deg, #666, #999);
            }
            
            .reflection-stats {
                margin-top: 10px;
                font-size: 12px;
                opacity: 0.8;
            }
            
            .reflection-question {
                background: rgba(255, 105, 180, 0.1);
                border-left: 3px solid #ff69b4;
                padding: 10px;
                margin: 10px 0;
                border-radius: 5px;
                font-style: italic;
            }
        `;
        document.head.appendChild(style);
        
        // Ajouter les événements
        this.setupReflectionEvents();
    }
    
    setupReflectionEvents() {
        const toggleBtn = document.getElementById('toggle-reflection');
        const statusSpan = document.getElementById('reflection-status');
        
        toggleBtn.addEventListener('click', () => {
            this.reflectionEnabled = !this.reflectionEnabled;
            
            if (this.reflectionEnabled) {
                toggleBtn.classList.add('active');
                toggleBtn.classList.remove('inactive');
                toggleBtn.querySelector('.status').textContent = 'ACTIF';
                statusSpan.textContent = 'Activé';
            } else {
                toggleBtn.classList.remove('active');
                toggleBtn.classList.add('inactive');
                toggleBtn.querySelector('.status').textContent = 'PAUSE';
                statusSpan.textContent = 'En pause';
            }
            
            // Notifier le serveur
            fetch('/api/reflection/toggle', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ enabled: this.reflectionEnabled })
            });
        });
    }
    
    showReflectionQuestion(question, category) {
        if (!this.reflectionEnabled) return;
        
        const chatContainer = document.getElementById('chat-container');
        if (!chatContainer) return;
        
        const questionDiv = document.createElement('div');
        questionDiv.className = 'reflection-question';
        questionDiv.innerHTML = `
            <strong>🧠 Réflexion (${category}):</strong><br>
            ${question}
        `;
        
        chatContainer.appendChild(questionDiv);
        chatContainer.scrollTop = chatContainer.scrollHeight;
        
        // Mettre à jour le compteur
        const countSpan = document.getElementById('reflection-count');
        if (countSpan) {
            const current = parseInt(countSpan.textContent) || 0;
            countSpan.textContent = current + 1;
        }
    }
}

// Initialiser le système de réflexion
const reflectionInterface = new ReflectionInterface();
