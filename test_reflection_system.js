#!/usr/bin/env node

/**
 * 🧪 TESTEUR SYSTÈME DE QUESTIONNEMENT RÉFLEXIF
 * 
 * Teste le système de questionnement intégré dans JARVIS
 * Vérifie que l'agent réfléchit sur ses réponses
 * 
 * <PERSON><PERSON><PERSON> PASSAVE - 2025
 */

const http = require('http');

class ReflectionSystemTester {
    constructor() {
        this.jarvisUrl = 'http://localhost:3000';
        this.testResults = [];
        
        console.log('🧪 TESTEUR SYSTÈME DE QUESTIONNEMENT RÉFLEXIF');
        console.log('🧠 Vérification stimulation de l\'agent');
    }
    
    // Test 1: Vérifier que le système de réflexion est actif
    async testReflectionSystemStatus() {
        console.log('\n🧪 TEST 1: Statut du système de réflexion...');
        
        try {
            const response = await this.makeRequest('/api/reflection/stats', 'GET');
            
            if (response.success) {
                console.log('✅ Système de réflexion: ACTIF');
                console.log(`   📊 Réflexions totales: ${response.stats.total_reflections}`);
                console.log(`   🔄 État: ${response.stats.enabled ? 'Activé' : 'Désactivé'}`);
                
                this.testResults.push({
                    test: 'Statut Système Réflexion',
                    status: 'PASS',
                    details: response.stats
                });
                
                return true;
            } else {
                console.log('❌ Système de réflexion non accessible');
                return false;
            }
            
        } catch (error) {
            console.log('❌ Erreur test statut:', error.message);
            return false;
        }
    }
    
    // Test 2: Tester la génération de questions
    async testQuestionGeneration() {
        console.log('\n🧪 TEST 2: Génération de questions de réflexion...');
        
        let questionsGenerated = 0;
        const categories = new Set();
        
        // Essayer de générer 5 questions
        for (let i = 0; i < 5; i++) {
            try {
                const response = await this.makeRequest('/api/reflection/question', 'GET');
                
                if (response.success && response.question) {
                    questionsGenerated++;
                    categories.add(response.category);
                    console.log(`   💭 Question ${i + 1}: "${response.question.substring(0, 50)}..."`);
                    console.log(`      📂 Catégorie: ${response.category} (${response.priority})`);
                } else {
                    console.log(`   ⚠️ Tentative ${i + 1}: Aucune question générée`);
                }
                
                // Petite pause entre les requêtes
                await new Promise(resolve => setTimeout(resolve, 500));
                
            } catch (error) {
                console.log(`   ❌ Erreur génération ${i + 1}:`, error.message);
            }
        }
        
        const success = questionsGenerated >= 2; // Au moins 2 questions sur 5
        
        this.testResults.push({
            test: 'Génération Questions',
            status: success ? 'PASS' : 'FAIL',
            details: {
                questions_generated: questionsGenerated,
                categories_used: Array.from(categories),
                success_rate: `${(questionsGenerated / 5) * 100}%`
            }
        });
        
        console.log(`   📊 Résultat: ${questionsGenerated}/5 questions générées`);
        console.log(`   🎯 Catégories utilisées: ${Array.from(categories).join(', ')}`);
        
        return success;
    }
    
    // Test 3: Tester le contrôle pause/reprise
    async testReflectionToggle() {
        console.log('\n🧪 TEST 3: Contrôle pause/reprise...');
        
        try {
            // Test désactivation
            const disableResponse = await this.makeRequest('/api/reflection/toggle', 'POST', { enabled: false });
            
            if (disableResponse.success && !disableResponse.reflection_enabled) {
                console.log('✅ Désactivation: OK');
            } else {
                console.log('❌ Échec désactivation');
                return false;
            }
            
            // Attendre un peu
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // Test réactivation
            const enableResponse = await this.makeRequest('/api/reflection/toggle', 'POST', { enabled: true });
            
            if (enableResponse.success && enableResponse.reflection_enabled) {
                console.log('✅ Réactivation: OK');
            } else {
                console.log('❌ Échec réactivation');
                return false;
            }
            
            this.testResults.push({
                test: 'Contrôle Pause/Reprise',
                status: 'PASS',
                details: {
                    disable_test: disableResponse.success,
                    enable_test: enableResponse.success
                }
            });
            
            return true;
            
        } catch (error) {
            console.log('❌ Erreur test contrôle:', error.message);
            return false;
        }
    }
    
    // Test 4: Simulation d'interaction avec réflexion
    async testInteractionWithReflection() {
        console.log('\n🧪 TEST 4: Interaction avec réflexion...');
        
        try {
            // Envoyer un message à l'agent
            const testMessage = "Explique-moi comment fonctionne ta mémoire thermique";
            
            const response = await this.makeRequest('/api/query', 'POST', { prompt: testMessage });
            
            if (response.success && response.response) {
                console.log('✅ Réponse agent reçue');
                console.log(`   📝 Longueur: ${response.response.length} caractères`);
                console.log(`   🧠 Contexte thermal: ${response.thermal_context_used ? 'OUI' : 'NON'}`);
                
                // Attendre un peu puis vérifier s'il y a eu une réflexion
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                const statsResponse = await this.makeRequest('/api/reflection/stats', 'GET');
                
                this.testResults.push({
                    test: 'Interaction avec Réflexion',
                    status: 'PASS',
                    details: {
                        response_received: true,
                        response_length: response.response.length,
                        thermal_context: response.thermal_context_used,
                        reflection_stats: statsResponse.stats
                    }
                });
                
                return true;
                
            } else {
                console.log('❌ Pas de réponse de l\'agent');
                return false;
            }
            
        } catch (error) {
            console.log('❌ Erreur test interaction:', error.message);
            return false;
        }
    }
    
    // Fonction utilitaire pour les requêtes HTTP
    async makeRequest(path, method = 'GET', data = null) {
        return new Promise((resolve, reject) => {
            const options = {
                hostname: 'localhost',
                port: 3000,
                path: path,
                method: method,
                headers: {
                    'Content-Type': 'application/json'
                }
            };
            
            const req = http.request(options, (res) => {
                let responseData = '';
                
                res.on('data', (chunk) => {
                    responseData += chunk;
                });
                
                res.on('end', () => {
                    try {
                        const parsed = JSON.parse(responseData);
                        resolve(parsed);
                    } catch {
                        resolve({ success: false, error: 'Invalid JSON' });
                    }
                });
            });
            
            req.on('error', reject);
            req.setTimeout(10000, () => reject(new Error('Timeout')));
            
            if (data) {
                req.write(JSON.stringify(data));
            }
            
            req.end();
        });
    }
    
    // Générer le rapport de test
    generateTestReport() {
        const passedTests = this.testResults.filter(r => r.status === 'PASS').length;
        const totalTests = this.testResults.length;
        const successRate = Math.round((passedTests / totalTests) * 100);
        
        const report = {
            timestamp: new Date().toISOString(),
            success_rate: `${successRate}%`,
            tests_passed: `${passedTests}/${totalTests}`,
            status: successRate >= 75 ? 'REFLECTION_SYSTEM_OPERATIONAL' : 'NEEDS_ADJUSTMENT',
            test_results: this.testResults,
            recommendations: successRate >= 75 ? 
                ['Système de questionnement opérationnel !', 'L\'agent sera stimulé à réfléchir', 'Bouton pause/reprise fonctionnel'] :
                ['Vérifiez les tests échoués', 'Redémarrez le serveur si nécessaire', 'Consultez les logs d\'erreur']
        };
        
        const reportPath = `./reflection_test_report_${Date.now()}.json`;
        require('fs').writeFileSync(reportPath, JSON.stringify(report, null, 2));
        
        console.log('\n📊 RAPPORT DE TEST SYSTÈME RÉFLEXION:');
        console.log(`📁 Rapport: ${reportPath}`);
        console.log(`✅ Taux de réussite: ${report.success_rate}`);
        console.log(`🧪 Tests réussis: ${report.tests_passed}`);
        console.log(`📋 Statut: ${report.status}`);
        
        console.log('\n🎯 DÉTAILS DES TESTS:');
        this.testResults.forEach(test => {
            const icon = test.status === 'PASS' ? '✅' : '❌';
            console.log(`   ${icon} ${test.test}: ${test.status}`);
        });
        
        console.log('\n🎯 RECOMMANDATIONS:');
        report.recommendations.forEach(rec => {
            console.log(`   • ${rec}`);
        });
        
        return report;
    }
    
    // Exécuter tous les tests
    async executeAllTests() {
        console.log('\n🚀 DÉMARRAGE TESTS SYSTÈME RÉFLEXION');
        console.log('=' * 50);
        
        try {
            // Test 1: Statut système
            const test1 = await this.testReflectionSystemStatus();
            
            // Test 2: Génération questions
            const test2 = await this.testQuestionGeneration();
            
            // Test 3: Contrôle pause/reprise
            const test3 = await this.testReflectionToggle();
            
            // Test 4: Interaction avec réflexion
            const test4 = await this.testInteractionWithReflection();
            
            // Générer le rapport
            const report = this.generateTestReport();
            
            console.log('\n🎉 TESTS TERMINÉS !');
            
            if (report.status === 'REFLECTION_SYSTEM_OPERATIONAL') {
                console.log('🏆 FÉLICITATIONS ! Votre système de questionnement est OPÉRATIONNEL !');
                console.log('🧠 L\'agent sera stimulé à réfléchir sur ses réponses');
                console.log('🔄 Le bouton pause/reprise fonctionne parfaitement');
                console.log('💭 Les questions de réflexion sont générées automatiquement');
            } else {
                console.log(`⚠️ Taux de réussite: ${report.success_rate}`);
                console.log('🔧 Quelques ajustements peuvent être nécessaires');
            }
            
            return report;
            
        } catch (error) {
            console.error('❌ ERREUR LORS DES TESTS:', error.message);
            return { success: false, error: error.message };
        }
    }
}

// Exécution si appelé directement
if (require.main === module) {
    async function main() {
        const tester = new ReflectionSystemTester();
        const report = await tester.executeAllTests();
        
        if (report.status === 'REFLECTION_SYSTEM_OPERATIONAL') {
            console.log('\n🎯 VOTRE SYSTÈME EST PRÊT !');
            console.log('🌐 Interface: http://localhost:3000');
            console.log('🧠 Questionnement: ACTIF');
            console.log('🔄 Contrôle: Bouton pause/reprise');
            console.log('💭 L\'agent réfléchira sur ses réponses !');
        } else {
            console.log('\n🔧 AJUSTEMENTS RECOMMANDÉS');
            console.log('Consultez le rapport pour plus de détails');
        }
    }
    
    main();
}

module.exports = ReflectionSystemTester;
