#!/usr/bin/env node

/**
 * 🧪 TEST SIMPLE SYSTÈME RÉFLEXION
 * 
 * Test rapide du système de questionnement
 * 
 * <PERSON><PERSON><PERSON> PASSAVE - 2025
 */

const http = require('http');

async function testReflection() {
    console.log('🧪 TEST SIMPLE SYSTÈME RÉFLEXION');
    
    // Test 1: Envoyer un message
    console.log('\n1. Test envoi message...');
    
    const testMessage = "Bonjour JARVIS, comment vas-tu ?";
    
    const postData = JSON.stringify({ prompt: testMessage });
    
    const options = {
        hostname: 'localhost',
        port: 3000,
        path: '/api/query',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Content-Length': Buffer.byteLength(postData)
        }
    };
    
    return new Promise((resolve, reject) => {
        const req = http.request(options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                try {
                    const response = JSON.parse(data);
                    console.log('✅ Réponse reçue !');
                    console.log(`📝 Longueur: ${response.response?.length || 0} caractères`);
                    console.log(`🧠 Contexte thermal: ${response.thermal_context_used ? 'OUI' : 'NON'}`);
                    
                    // Test 2: Demander une question de réflexion
                    console.log('\n2. Test génération question réflexion...');
                    
                    const questionReq = http.request({
                        hostname: 'localhost',
                        port: 3000,
                        path: '/api/reflection/question',
                        method: 'GET'
                    }, (questionRes) => {
                        let questionData = '';
                        
                        questionRes.on('data', (chunk) => {
                            questionData += chunk;
                        });
                        
                        questionRes.on('end', () => {
                            try {
                                const questionResponse = JSON.parse(questionData);
                                
                                if (questionResponse.success) {
                                    console.log('✅ Question de réflexion générée !');
                                    console.log(`💭 Question: "${questionResponse.question}"`);
                                    console.log(`📂 Catégorie: ${questionResponse.category}`);
                                    console.log(`⭐ Priorité: ${questionResponse.priority}`);
                                } else {
                                    console.log('⚠️ Aucune question générée');
                                }
                                
                                resolve(true);
                                
                            } catch (error) {
                                console.log('❌ Erreur parsing question:', error.message);
                                resolve(false);
                            }
                        });
                    });
                    
                    questionReq.on('error', (error) => {
                        console.log('❌ Erreur requête question:', error.message);
                        resolve(false);
                    });
                    
                    questionReq.end();
                    
                } catch (error) {
                    console.log('❌ Erreur parsing réponse:', error.message);
                    resolve(false);
                }
            });
        });
        
        req.on('error', (error) => {
            console.log('❌ Erreur requête:', error.message);
            resolve(false);
        });
        
        req.write(postData);
        req.end();
    });
}

// Exécuter le test
testReflection().then(success => {
    if (success) {
        console.log('\n🎉 TEST RÉUSSI !');
        console.log('🧠 Le système de questionnement fonctionne');
        console.log('🌐 Votre interface est prête sur http://localhost:3000');
        console.log('🔄 Utilisez le bouton pour contrôler la réflexion');
    } else {
        console.log('\n❌ TEST ÉCHOUÉ');
        console.log('Vérifiez que le serveur fonctionne correctement');
    }
}).catch(error => {
    console.log('❌ ERREUR TEST:', error.message);
});
